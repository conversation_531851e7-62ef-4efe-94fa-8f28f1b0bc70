# Analytics Dashboard with PostHog Integration

## Overview

The Analytics Dashboard provides comprehensive business intelligence and user behavior tracking for the Maomao e-commerce admin platform. It integrates with PostHog for advanced analytics, event tracking, and user behavior analysis.

## Features

### 📊 **Dashboard Analytics**
- **Real-time Metrics**: Live data on active users, current orders, revenue, and group buys
- **Key Performance Indicators**: Revenue growth, order growth, user growth, conversion rates
- **Interactive Charts**: Revenue trends, user growth, product performance, conversion funnels
- **Top Products**: Best-selling products with revenue and sales data
- **Recent Activity**: Live feed of system activities and user actions

### 📈 **Revenue Analytics**
- Revenue trends over time with customizable periods
- Revenue breakdown by category and source (regular vs group buy)
- Top customers by spending and order frequency
- Average order value tracking
- Revenue growth comparisons

### 👥 **User Analytics**
- User growth and acquisition trends
- User demographics by country and registration source
- User behavior metrics (session duration, repeat purchase rate, churn rate)
- Cohort analysis for retention tracking
- User engagement patterns

### 🛍️ **Product Analytics**
- Product performance metrics (views, orders, conversion rates)
- Category performance analysis
- Inventory analytics (low stock, out of stock, turnover rates)
- Product conversion scatter plots
- Stock level monitoring

### 🤝 **Group Buy Analytics**
- Group buy success rates and performance metrics
- Participation trends and user engagement
- Group buy status distribution
- Total savings generated for customers
- Campaign effectiveness analysis

### 🔄 **Conversion Analytics**
- Conversion funnel visualization
- Conversion rates by traffic source and device
- Drop-off analysis at each funnel stage
- A/B testing results integration
- User journey optimization insights

## PostHog Integration

### Event Tracking

The system automatically tracks comprehensive admin actions:

#### **User Management Events**
- `admin_user_viewed` - When admin views user details
- `admin_user_updated` - When admin updates user information
- `admin_user_status_changed` - When admin changes user status
- `admin_users_exported` - When admin exports user data

#### **Product Management Events**
- `admin_product_created` - When admin creates new product
- `admin_product_updated` - When admin updates product
- `admin_variant_created` - When admin creates product variant
- `admin_products_imported` - When admin imports products
- `admin_products_exported` - When admin exports products

#### **Order Management Events**
- `admin_order_viewed` - When admin views order details
- `admin_order_status_updated` - When admin updates order status
- `admin_order_issue_created` - When admin creates order issue
- `admin_payment_verified` - When admin verifies payment
- `admin_order_refund_processed` - When admin processes refund

#### **Group Buy Management Events**
- `admin_group_buy_created` - When admin creates group buy
- `admin_group_buy_status_changed` - When admin changes group buy status
- `admin_group_buy_deadline_extended` - When admin extends deadline
- `admin_group_buy_participant_added` - When admin adds participant
- `admin_group_buy_notification_sent` - When admin sends notifications

#### **Analytics Events**
- `admin_dashboard_viewed` - When admin views dashboard sections
- `admin_analytics_viewed` - When admin views analytics
- `admin_report_generated` - When admin generates reports
- `admin_filter_applied` - When admin applies filters
- `admin_search_performed` - When admin performs searches

### User Identification

PostHog automatically identifies admin users with:
- Admin ID and email
- Admin role (admin vs super admin)
- Login/logout tracking
- Session duration monitoring
- Activity patterns

### Custom Properties

Each event includes relevant properties:
- Entity IDs (user, product, order, group buy)
- Action types and contexts
- Business metrics (revenue, quantities, dates)
- Performance metrics (load times, response times)
- Error tracking and debugging information

## Setup Instructions

### 1. PostHog Configuration

1. Create a PostHog account at [posthog.com](https://posthog.com)
2. Create a new project for your admin dashboard
3. Copy your Project API Key and Host URL
4. Add to your environment variables:

```env
NEXT_PUBLIC_POSTHOG_KEY=your_posthog_project_api_key
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com
```

### 2. Environment Setup

Ensure all required environment variables are set:

```env
# PostHog Configuration
NEXT_PUBLIC_POSTHOG_KEY=phc_your_key_here
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com

# Firebase Configuration (for data source)
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
# ... other Firebase config

# API Configuration
NEXT_PUBLIC_API_BASE_URL=https://your-api-endpoint
```

### 3. Backend Integration

Ensure your Firebase Cloud Functions provide the following analytics endpoints:

```
GET /api/admin/analytics/overview
GET /api/admin/analytics/revenue
GET /api/admin/analytics/users
GET /api/admin/analytics/products
GET /api/admin/analytics/group-buys
GET /api/admin/analytics/conversion
GET /api/admin/analytics/performance
GET /api/admin/analytics/realtime
POST /api/admin/analytics/export
```

## Usage Guide

### Accessing Analytics

1. Navigate to the Analytics section in the admin dashboard
2. Use the date range picker to filter data by time period
3. Select different tabs to view specific analytics categories
4. Apply filters to drill down into specific data segments

### Generating Reports

1. Click the "Export Report" button in any analytics section
2. Select the report type and date range
3. The system will generate and download a comprehensive report
4. Reports include charts, tables, and summary statistics

### Real-time Monitoring

The dashboard automatically refreshes real-time metrics every 30 seconds:
- Active users currently on the platform
- Orders being processed
- Revenue generated today
- Active group buy campaigns

### Custom Dashboards

Create custom dashboard views by:
1. Selecting specific metrics and time ranges
2. Combining multiple chart types
3. Setting up automated report generation
4. Configuring alert thresholds

## PostHog Dashboard Setup

### Recommended Insights

1. **Admin Activity Funnel**
   - Track admin login → dashboard view → action completion
   - Identify workflow bottlenecks

2. **Feature Usage Trends**
   - Monitor which admin features are used most
   - Track feature adoption over time

3. **Performance Monitoring**
   - Page load times across different sections
   - API response time trends
   - Error rate monitoring

4. **Business Impact Tracking**
   - Correlation between admin actions and business metrics
   - Revenue impact of admin interventions
   - Efficiency improvements over time

### Custom Events

You can track additional custom events using the tracking utilities:

```typescript
import { trackEvent } from '@/lib/posthog/config';

// Track custom admin action
trackEvent('admin_custom_action', {
  action_type: 'bulk_update',
  entity_count: 50,
  success: true,
});
```

## Troubleshooting

### Common Issues

1. **PostHog not tracking events**
   - Verify API key and host URL are correct
   - Check browser console for PostHog errors
   - Ensure PostHog script is loaded

2. **Analytics data not loading**
   - Verify backend API endpoints are accessible
   - Check network requests in browser dev tools
   - Confirm Firebase authentication is working

3. **Charts not rendering**
   - Verify Recharts library is installed
   - Check for JavaScript errors in console
   - Ensure data format matches chart expectations

### Debug Mode

Enable PostHog debug mode in development:

```typescript
// In posthog/config.ts
loaded: (posthog: any) => {
  if (process.env.NODE_ENV === 'development') posthog.debug();
}
```

## Performance Considerations

- Analytics data is cached for 5 minutes to reduce API calls
- Real-time metrics refresh every 30 seconds
- Large datasets are paginated to improve load times
- Charts use responsive containers for optimal rendering
- PostHog events are batched for efficient transmission

## Security & Privacy

- All analytics data is transmitted over HTTPS
- PostHog respects user privacy settings
- Admin actions are logged for audit purposes
- Sensitive data is excluded from tracking
- GDPR compliance through PostHog's privacy controls
