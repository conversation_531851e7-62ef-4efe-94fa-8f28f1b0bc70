"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _CustomerServiceFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/CustomerServiceFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var CustomerServiceFilled = function CustomerServiceFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _CustomerServiceFilled.default
  }));
};

/**![customer-service](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiAxMjhjLTIxMi4xIDAtMzg0IDE3MS45LTM4NCAzODR2MzYwYzAgMTMuMyAxMC43IDI0IDI0IDI0aDE4NGMzNS4zIDAgNjQtMjguNyA2NC02NFY2MjRjMC0zNS4zLTI4LjctNjQtNjQtNjRIMjAwdi00OGMwLTE3Mi4zIDEzOS43LTMxMiAzMTItMzEyczMxMiAxMzkuNyAzMTIgMzEydjQ4SDY4OGMtMzUuMyAwLTY0IDI4LjctNjQgNjR2MjA4YzAgMzUuMyAyOC43IDY0IDY0IDY0aDE4NGMxMy4zIDAgMjQtMTAuNyAyNC0yNFY1MTJjMC0yMTIuMS0xNzEuOS0zODQtMzg0LTM4NHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(CustomerServiceFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CustomerServiceFilled';
}
var _default = exports.default = RefIcon;