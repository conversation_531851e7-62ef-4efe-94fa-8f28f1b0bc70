{"version": 3, "file": "no-unnecessary-boolean-literal-compare.js", "sourceRoot": "", "sources": ["../../src/rules/no-unnecessary-boolean-literal-compare.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,sDAAwC;AACxC,+CAAiC;AAEjC,kCAAgF;AA0BhF,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,wCAAwC;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,oEAAoE;YACtE,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,MAAM,EACJ,mGAAmG;YACrG,OAAO,EACL,6FAA6F;YAC/F,6BAA6B,EAC3B,uGAAuG;YACzG,8BAA8B,EAC5B,iGAAiG;YACnG,wBAAwB,EACtB,iIAAiI;SACpI;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,oCAAoC,EAAE;wBACpC,WAAW,EACT,6EAA6E;wBAC/E,IAAI,EAAE,SAAS;qBAChB;oBACD,qCAAqC,EAAE;wBACrC,WAAW,EACT,8EAA8E;wBAChF,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;QACD,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE;QACd;YACE,oCAAoC,EAAE,IAAI;YAC1C,qCAAqC,EAAE,IAAI;SAC5C;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAE5C,SAAS,oBAAoB,CAC3B,IAA+B;YAE/B,MAAM,UAAU,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAC/C,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,MAAM,cAAc,GAAG,QAAQ,CAAC,iBAAiB,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAEzE,IAAI,aAAa,CAAC,cAAc,CAAC,EAAE,CAAC;gBAClC,OAAO;oBACL,GAAG,UAAU;oBACb,2BAA2B,EAAE,KAAK;iBACnC,CAAC;YACJ,CAAC;YAED,IAAI,iBAAiB,CAAC,cAAc,CAAC,EAAE,CAAC;gBACtC,OAAO;oBACL,GAAG,UAAU;oBACb,2BAA2B,EAAE,IAAI;iBAClC,CAAC;YACJ,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,SAAS,aAAa,CAAC,cAAuB;YAC5C,OAAO,OAAO,CAAC,aAAa,CAC1B,cAAc,EACd,EAAE,CAAC,SAAS,CAAC,OAAO,GAAG,EAAE,CAAC,SAAS,CAAC,cAAc,CACnD,CAAC;QACJ,CAAC;QAED;;;;;WAKG;QACH,SAAS,iBAAiB,CAAC,cAAuB;YAChD,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;gBAC9B,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,EAAE,KAAK,EAAE,GAAG,cAAc,CAAC;YAEjC,MAAM,eAAe,GAAG,KAAK,CAAC,MAAM,CAClC,IAAI,CAAC,EAAE,CACL,CAAC,OAAO,CAAC,aAAa,CACpB,IAAI,EACJ,EAAE,CAAC,SAAS,CAAC,SAAS,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,CAC3C,CACJ,CAAC;YAEF,MAAM,iBAAiB,GAAG,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;YACrD,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACvB,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,eAAe,GAAG,eAAe,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAC9D,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,4BAA4B,GAAG,eAAe,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAC1E,IAAI,CAAC,4BAA4B,EAAE,CAAC;gBAClC,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED,SAAS,qBAAqB,CAC5B,IAA+B;YAE/B,MAAM,cAAc,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpD,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,KAAK,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,IAAI;gBAClC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC;gBACvB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC;aACxB,EAAE,CAAC;gBACF,IACE,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;oBACvC,OAAO,OAAO,CAAC,KAAK,KAAK,SAAS,EAClC,CAAC;oBACD,SAAS;gBACX,CAAC;gBAED,MAAM,EAAE,KAAK,EAAE,0BAA0B,EAAE,GAAG,OAAO,CAAC;gBACtD,MAAM,OAAO,GAAG,CAAC,cAAc,CAAC,UAAU,CAAC;gBAE3C,OAAO;oBACL,0BAA0B;oBAC1B,UAAU;oBACV,OAAO;iBACR,CAAC;YACJ,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,SAAS,mBAAmB,CAAC,IAAmB;YAC9C,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBAC5C,IAAI,CAAC,MAAM;gBACX,IAAI,CAAC,QAAQ,KAAK,GAAG,CACtB,CAAC;QACJ,CAAC;QAED,OAAO;YACL,gBAAgB,CAAC,IAAI;gBACnB,MAAM,UAAU,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC;gBAC9C,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;oBAC7B,OAAO;gBACT,CAAC;gBAED,IAAI,UAAU,CAAC,2BAA2B,EAAE,CAAC;oBAC3C,IACE,UAAU,CAAC,0BAA0B;wBACrC,OAAO,CAAC,oCAAoC,EAC5C,CAAC;wBACD,OAAO;oBACT,CAAC;oBACD,IACE,CAAC,UAAU,CAAC,0BAA0B;wBACtC,OAAO,CAAC,qCAAqC,EAC7C,CAAC;wBACD,OAAO;oBACT,CAAC;gBACH,CAAC;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,GAAG,EAAE,QAAQ,CAAC,EAAE,KAAK;wBACnB,uCAAuC;wBACvC,iEAAiE;wBACjE,qCAAqC;wBAErC,MAAM,eAAe,GAAG,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAEzD,MAAM,YAAY,GAChB,UAAU,CAAC,OAAO,KAAK,UAAU,CAAC,0BAA0B,CAAC;wBAE/D,MAAM,WAAW,GAAG,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;wBAEzD,MAAM,KAAK,CAAC,WAAW,CACrB,WAAW,EACX,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,CAClD,CAAC;wBAEF,mGAAmG;wBACnG,IAAI,YAAY,KAAK,eAAe,EAAE,CAAC;4BACrC,MAAM,KAAK,CAAC,gBAAgB,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;4BAE/C,kFAAkF;4BAClF,IAAI,CAAC,IAAA,6BAAsB,EAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gCACnD,MAAM,KAAK,CAAC,gBAAgB,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;gCAC/C,MAAM,KAAK,CAAC,eAAe,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;4BAChD,CAAC;wBACH,CAAC;wBAED,2FAA2F;wBAC3F,IACE,UAAU,CAAC,2BAA2B;4BACtC,CAAC,UAAU,CAAC,0BAA0B,EACtC,CAAC;4BACD,6BAA6B;4BAC7B,MAAM,KAAK,CAAC,gBAAgB,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;4BAC/C,MAAM,KAAK,CAAC,eAAe,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;wBACxD,CAAC;oBACH,CAAC;oBACD,SAAS,EAAE,UAAU,CAAC,2BAA2B;wBAC/C,CAAC,CAAC,UAAU,CAAC,0BAA0B;4BACrC,CAAC,CAAC,UAAU,CAAC,OAAO;gCAClB,CAAC,CAAC,gCAAgC;gCAClC,CAAC,CAAC,+BAA+B;4BACnC,CAAC,CAAC,0BAA0B;wBAC9B,CAAC,CAAC,UAAU,CAAC,OAAO;4BAClB,CAAC,CAAC,SAAS;4BACX,CAAC,CAAC,QAAQ;oBACd,IAAI;iBACL,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAOH,SAAS,aAAa,CAAC,QAAgB;IACrC,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,IAAI;YACP,OAAO;gBACL,UAAU,EAAE,IAAI;gBAChB,QAAQ,EAAE,KAAK;aAChB,CAAC;QAEJ,KAAK,KAAK;YACR,OAAO;gBACL,UAAU,EAAE,IAAI;gBAChB,QAAQ,EAAE,IAAI;aACf,CAAC;QAEJ,KAAK,IAAI;YACP,OAAO;gBACL,UAAU,EAAE,KAAK;gBACjB,QAAQ,EAAE,KAAK;aAChB,CAAC;QAEJ,KAAK,KAAK;YACR,OAAO;gBACL,UAAU,EAAE,KAAK;gBACjB,QAAQ,EAAE,IAAI;aACf,CAAC;QAEJ;YACE,OAAO,SAAS,CAAC;IACrB,CAAC;AACH,CAAC"}