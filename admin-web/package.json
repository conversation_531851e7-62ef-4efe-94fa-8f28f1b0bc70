{"name": "maomao-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "clean": "rm -rf .next out dist"}, "dependencies": {"next": "14.2.5", "react": "^18.3.1", "react-dom": "^18.3.1", "typescript": "^5.5.4", "@types/node": "^20.14.12", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "firebase": "^10.12.4", "firebase-admin": "^12.2.0", "antd": "^5.19.3", "tailwindcss": "^3.4.7", "autoprefixer": "^10.4.19", "postcss": "^8.4.40", "@ant-design/icons": "^5.4.0", "@tanstack/react-query": "^5.51.11", "zustand": "^4.5.4", "react-hook-form": "^7.52.1", "@hookform/resolvers": "^3.9.0", "zod": "^3.23.8", "dayjs": "^1.11.12", "recharts": "^2.12.7", "lucide-react": "^0.417.0", "clsx": "^2.1.1", "class-variance-authority": "^0.7.0", "posthog-js": "^1.96.1", "posthog-node": "^3.6.3"}, "devDependencies": {"eslint": "^8.57.0", "eslint-config-next": "14.2.5", "@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.17.0", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.5"}}