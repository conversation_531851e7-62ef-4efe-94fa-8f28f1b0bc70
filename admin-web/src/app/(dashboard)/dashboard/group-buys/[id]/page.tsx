'use client';

import { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import {
  Card,
  Descriptions,
  Button,
  Space,
  Tag,
  Tabs,
  Table,
  Typography,
  Row,
  Col,
  Progress,
  Modal,
  Form,
  Input,
  DatePicker,
  Select,
  Statistic,
  Alert,
  Tooltip,
  Dropdown,
  Avatar,
  Timeline,
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  TeamOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  TrophyOutlined,
  UserOutlined,
  MoreOutlined,
  PlusOutlined,
  DeleteOutlined,
  MailOutlined,
  CalendarOutlined,
  BarChartOutlined,
} from '@ant-design/icons';
import {
  useGroupBuy,
  useGroupBuyParticipants,
  useGroupBuyMetrics,
  useUpdateGroupBuyStatus,
  useExtendDeadline,
  useForceComplete,
  useMarkFulfilled,
  useRemoveParticipant,
  useAddParticipant,
  useNotifyParticipants,
} from '@/lib/hooks/useGroupBuys';
import { GroupBuyParticipant } from '@/lib/api/groupbuys';
import { StatusBadge } from '@/components/ui/StatusBadge';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { formatDate, formatCurrency, formatRelativeTime } from '@/lib/utils/formatters';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

export default function GroupBuyDetailPage() {
  const params = useParams();
  const router = useRouter();
  const groupBuyId = params.id as string;

  const [statusModalVisible, setStatusModalVisible] = useState(false);
  const [extendModalVisible, setExtendModalVisible] = useState(false);
  const [fulfillModalVisible, setFulfillModalVisible] = useState(false);
  const [notifyModalVisible, setNotifyModalVisible] = useState(false);
  const [addParticipantModalVisible, setAddParticipantModalVisible] = useState(false);

  const [statusForm] = Form.useForm();
  const [extendForm] = Form.useForm();
  const [fulfillForm] = Form.useForm();
  const [notifyForm] = Form.useForm();
  const [participantForm] = Form.useForm();

  const { data: groupBuyData, isLoading: groupBuyLoading } = useGroupBuy(groupBuyId);
  const { data: participantsData } = useGroupBuyParticipants(groupBuyId);
  const { data: metricsData } = useGroupBuyMetrics(groupBuyId);

  const updateGroupBuyStatus = useUpdateGroupBuyStatus();
  const extendDeadline = useExtendDeadline();
  const forceComplete = useForceComplete();
  const markFulfilled = useMarkFulfilled();
  const removeParticipant = useRemoveParticipant();
  const addParticipant = useAddParticipant();
  const notifyParticipants = useNotifyParticipants();

  if (groupBuyLoading) {
    return <LoadingSpinner />;
  }

  if (!groupBuyData?.data) {
    return (
      <div className="text-center py-8">
        <Text type="secondary">Group buy not found</Text>
      </div>
    );
  }

  const groupBuy = groupBuyData.data;

  const handleUpdateStatus = async (values: any) => {
    try {
      await updateGroupBuyStatus.mutateAsync({
        groupBuyId,
        status: values.status,
        notes: values.notes,
      });
      setStatusModalVisible(false);
      statusForm.resetFields();
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleExtendDeadline = async (values: any) => {
    try {
      await extendDeadline.mutateAsync({
        groupBuyId,
        newExpiresAt: values.expiresAt.toDate(),
        reason: values.reason,
      });
      setExtendModalVisible(false);
      extendForm.resetFields();
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleMarkFulfilled = async (values: any) => {
    try {
      await markFulfilled.mutateAsync({
        groupBuyId,
        data: values,
      });
      setFulfillModalVisible(false);
      fulfillForm.resetFields();
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleNotifyParticipants = async (values: any) => {
    try {
      await notifyParticipants.mutateAsync({
        groupBuyId,
        data: values,
      });
      setNotifyModalVisible(false);
      notifyForm.resetFields();
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleAddParticipant = async (values: any) => {
    try {
      await addParticipant.mutateAsync({
        groupBuyId,
        data: values,
      });
      setAddParticipantModalVisible(false);
      participantForm.resetFields();
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleRemoveParticipant = (participantId: string) => {
    Modal.confirm({
      title: 'Remove Participant',
      content: 'Are you sure you want to remove this participant from the group buy?',
      onOk: () => removeParticipant.mutate({ groupBuyId, participantId }),
    });
  };

  const handleForceComplete = () => {
    Modal.confirm({
      title: 'Force Complete Group Buy',
      content: 'Are you sure you want to force complete this group buy even though the target quantity has not been reached?',
      onOk: () => {
        Modal.confirm({
          title: 'Reason for Force Completion',
          content: (
            <Input.TextArea
              placeholder="Please provide a reason for force completion..."
              rows={3}
              onChange={(e) => {
                // Store reason
              }}
            />
          ),
          onOk: () => {
            forceComplete.mutate({ groupBuyId, reason: 'Admin force completion' });
          },
        });
      },
    });
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 100) return '#52c41a';
    if (percentage >= 75) return '#1890ff';
    if (percentage >= 50) return '#fa8c16';
    return '#f5222d';
  };

  const getParticipantActions = (participant: GroupBuyParticipant) => [
    {
      key: 'view',
      label: 'View User',
      icon: <UserOutlined />,
      onClick: () => router.push(`/dashboard/users/${participant.userId}`),
    },
    {
      key: 'remove',
      label: 'Remove',
      icon: <DeleteOutlined />,
      danger: true,
      onClick: () => handleRemoveParticipant(participant.id),
    },
  ];

  const participantColumns = [
    {
      title: 'Participant',
      key: 'participant',
      render: (record: GroupBuyParticipant) => (
        <div className="flex items-center space-x-3">
          <Avatar icon={<UserOutlined />} />
          <div>
            <div className="font-medium">{record.user.displayName}</div>
            <div className="text-sm text-gray-500">{record.user.email}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity: number) => <Tag color="blue">{quantity}</Tag>,
    },
    {
      title: 'Joined',
      dataIndex: 'joinedAt',
      key: 'joinedAt',
      render: (date: Date) => (
        <Tooltip title={formatDate(date)}>
          {formatRelativeTime(date)}
        </Tooltip>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: GroupBuyParticipant) => (
        <Dropdown
          menu={{ items: getParticipantActions(record) }}
          trigger={['click']}
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  const tabItems = [
    {
      key: 'overview',
      label: 'Overview',
      children: (
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card title="Group Buy Information">
              <Descriptions column={1} size="small">
                <Descriptions.Item label="Product">
                  {groupBuy.productVariant.product.name} - {groupBuy.productVariant.name}
                </Descriptions.Item>
                <Descriptions.Item label="SKU">
                  {groupBuy.productVariant.sku}
                </Descriptions.Item>
                <Descriptions.Item label="Status">
                  <StatusBadge status={groupBuy.status} type="groupbuy" />
                </Descriptions.Item>
                <Descriptions.Item label="Target Quantity">
                  {groupBuy.targetQuantity}
                </Descriptions.Item>
                <Descriptions.Item label="Current Quantity">
                  {groupBuy.currentQuantity}
                </Descriptions.Item>
                <Descriptions.Item label="Group Price">
                  {formatCurrency(groupBuy.groupPrice, groupBuy.productVariant.currency)}
                </Descriptions.Item>
                <Descriptions.Item label="Regular Price">
                  {formatCurrency(groupBuy.productVariant.myPrice, groupBuy.productVariant.currency)}
                </Descriptions.Item>
                <Descriptions.Item label="Expires">
                  {formatDate(groupBuy.expiresAt)}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="Progress">
              <div className="space-y-4">
                <Progress
                  percent={groupBuy.progressPercentage}
                  strokeColor={getProgressColor(groupBuy.progressPercentage)}
                  format={() => `${groupBuy.currentQuantity}/${groupBuy.targetQuantity}`}
                />
                
                <Row gutter={16}>
                  <Col span={8}>
                    <Statistic
                      title="Participants"
                      value={groupBuy.participantCount}
                      prefix={<TeamOutlined />}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="Total Value"
                      value={groupBuy.totalValue}
                      formatter={(value) => formatCurrency(Number(value), groupBuy.productVariant.currency)}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="Savings"
                      value={groupBuy.estimatedSavings}
                      formatter={(value) => formatCurrency(Number(value), groupBuy.productVariant.currency)}
                      valueStyle={{ color: '#52c41a' }}
                    />
                  </Col>
                </Row>

                {groupBuy.timeRemaining > 0 && groupBuy.status === 'Active' && (
                  <Alert
                    message={`Time Remaining: ${Math.ceil(groupBuy.timeRemaining / (1000 * 60 * 60))} hours`}
                    type={groupBuy.timeRemaining < 24 * 60 * 60 * 1000 ? 'warning' : 'info'}
                    showIcon
                  />
                )}
              </div>
            </Card>
          </Col>
        </Row>
      ),
    },
    {
      key: 'participants',
      label: `Participants (${groupBuy.participantCount})`,
      icon: <TeamOutlined />,
      children: (
        <Card
          title="Group Buy Participants"
          extra={
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setAddParticipantModalVisible(true)}
            >
              Add Participant
            </Button>
          }
        >
          <Table
            columns={participantColumns}
            dataSource={participantsData?.data || []}
            rowKey="id"
            pagination={false}
            scroll={{ x: 600 }}
            locale={{ emptyText: 'No participants yet' }}
          />
        </Card>
      ),
    },
    {
      key: 'metrics',
      label: 'Analytics',
      icon: <BarChartOutlined />,
      children: (
        <Card title="Group Buy Metrics">
          {metricsData ? (
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} lg={6}>
                <Statistic
                  title="Conversion Rate"
                  value={metricsData.data.conversionRate}
                  suffix="%"
                  precision={1}
                />
              </Col>
              <Col xs={24} sm={12} lg={6}>
                <Statistic
                  title="Share Rate"
                  value={metricsData.data.shareRate}
                  suffix="%"
                  precision={1}
                />
              </Col>
              <Col xs={24} sm={12} lg={6}>
                <Statistic
                  title="New Users"
                  value={metricsData.data.participantDemographics.newUsers}
                />
              </Col>
              <Col xs={24} sm={12} lg={6}>
                <Statistic
                  title="Completion Probability"
                  value={metricsData.data.completionProbability}
                  suffix="%"
                  precision={1}
                />
              </Col>
            </Row>
          ) : (
            <div className="text-center py-8">
              <Text type="secondary">Analytics data not available</Text>
            </div>
          )}
        </Card>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => router.back()}
          >
            Back
          </Button>
          <div>
            <Title level={3} className="!mb-0">
              {groupBuy.productVariant.product.name}
            </Title>
            <Text type="secondary">
              {groupBuy.productVariant.name} • Created {formatDate(groupBuy.createdAt)}
            </Text>
          </div>
        </div>

        <Space>
          <Button
            icon={<EditOutlined />}
            onClick={() => setStatusModalVisible(true)}
          >
            Update Status
          </Button>
          <Button
            icon={<CalendarOutlined />}
            onClick={() => setExtendModalVisible(true)}
            disabled={groupBuy.status !== 'Active'}
          >
            Extend Deadline
          </Button>
          <Button
            icon={<MailOutlined />}
            onClick={() => setNotifyModalVisible(true)}
          >
            Notify Participants
          </Button>
          <Button
            icon={<TrophyOutlined />}
            onClick={handleForceComplete}
            disabled={groupBuy.status !== 'Active'}
          >
            Force Complete
          </Button>
          <Button
            icon={<CheckCircleOutlined />}
            onClick={() => setFulfillModalVisible(true)}
            disabled={groupBuy.status !== 'Successful'}
          >
            Mark Fulfilled
          </Button>
        </Space>
      </div>

      {/* Alert for Status */}
      {groupBuy.status === 'Active' && groupBuy.progressPercentage >= 100 && (
        <Alert
          message="Target Reached!"
          description="This group buy has reached its target quantity and can be marked as successful."
          type="success"
          showIcon
          action={
            <Button size="small" type="primary" onClick={() => handleUpdateStatus({ status: 'Successful' })}>
              Mark Successful
            </Button>
          }
        />
      )}

      {groupBuy.status === 'Active' && groupBuy.timeRemaining < 24 * 60 * 60 * 1000 && (
        <Alert
          message="Expiring Soon"
          description={`This group buy will expire in ${Math.ceil(groupBuy.timeRemaining / (1000 * 60 * 60))} hours.`}
          type="warning"
          showIcon
        />
      )}

      {/* Content Tabs */}
      <Tabs items={tabItems} />

      {/* Status Update Modal */}
      <Modal
        title="Update Group Buy Status"
        open={statusModalVisible}
        onCancel={() => setStatusModalVisible(false)}
        onOk={() => statusForm.submit()}
        confirmLoading={updateGroupBuyStatus.isPending}
      >
        <Form
          form={statusForm}
          layout="vertical"
          onFinish={handleUpdateStatus}
        >
          <Form.Item
            name="status"
            label="New Status"
            rules={[{ required: true, message: 'Please select status' }]}
          >
            <Select>
              <Select.Option value="Active">Active</Select.Option>
              <Select.Option value="Successful">Successful</Select.Option>
              <Select.Option value="Failed">Failed</Select.Option>
              <Select.Option value="Fulfilled">Fulfilled</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item name="notes" label="Notes">
            <TextArea rows={3} placeholder="Optional notes..." />
          </Form.Item>
        </Form>
      </Modal>

      {/* Extend Deadline Modal */}
      <Modal
        title="Extend Deadline"
        open={extendModalVisible}
        onCancel={() => setExtendModalVisible(false)}
        onOk={() => extendForm.submit()}
        confirmLoading={extendDeadline.isPending}
      >
        <Form
          form={extendForm}
          layout="vertical"
          onFinish={handleExtendDeadline}
        >
          <Form.Item
            name="expiresAt"
            label="New Expiration Date"
            rules={[{ required: true, message: 'Please select new expiration date' }]}
          >
            <DatePicker
              showTime
              style={{ width: '100%' }}
              disabledDate={(current) => current && current < dayjs().endOf('day')}
            />
          </Form.Item>
          <Form.Item
            name="reason"
            label="Reason"
            rules={[{ required: true, message: 'Please provide reason' }]}
          >
            <TextArea rows={3} placeholder="Reason for extending deadline..." />
          </Form.Item>
        </Form>
      </Modal>

      {/* Mark Fulfilled Modal */}
      <Modal
        title="Mark as Fulfilled"
        open={fulfillModalVisible}
        onCancel={() => setFulfillModalVisible(false)}
        onOk={() => fulfillForm.submit()}
        confirmLoading={markFulfilled.isPending}
      >
        <Form
          form={fulfillForm}
          layout="vertical"
          onFinish={handleMarkFulfilled}
        >
          <Form.Item name="fulfillmentNotes" label="Fulfillment Notes">
            <TextArea rows={3} placeholder="Notes about fulfillment..." />
          </Form.Item>
          <Form.Item name="estimatedDelivery" label="Estimated Delivery">
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
        </Form>
      </Modal>

      {/* Notify Participants Modal */}
      <Modal
        title="Notify Participants"
        open={notifyModalVisible}
        onCancel={() => setNotifyModalVisible(false)}
        onOk={() => notifyForm.submit()}
        confirmLoading={notifyParticipants.isPending}
      >
        <Form
          form={notifyForm}
          layout="vertical"
          onFinish={handleNotifyParticipants}
        >
          <Form.Item
            name="type"
            label="Notification Type"
            rules={[{ required: true, message: 'Please select type' }]}
          >
            <Select>
              <Select.Option value="info">Info</Select.Option>
              <Select.Option value="success">Success</Select.Option>
              <Select.Option value="warning">Warning</Select.Option>
              <Select.Option value="error">Error</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="title"
            label="Title"
            rules={[{ required: true, message: 'Please enter title' }]}
          >
            <Input placeholder="Notification title" />
          </Form.Item>
          <Form.Item
            name="message"
            label="Message"
            rules={[{ required: true, message: 'Please enter message' }]}
          >
            <TextArea rows={4} placeholder="Notification message" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Add Participant Modal */}
      <Modal
        title="Add Participant"
        open={addParticipantModalVisible}
        onCancel={() => setAddParticipantModalVisible(false)}
        onOk={() => participantForm.submit()}
        confirmLoading={addParticipant.isPending}
      >
        <Form
          form={participantForm}
          layout="vertical"
          onFinish={handleAddParticipant}
        >
          <Form.Item
            name="userId"
            label="User ID"
            rules={[{ required: true, message: 'Please enter user ID' }]}
          >
            <Input placeholder="Enter user ID" />
          </Form.Item>
          <Form.Item
            name="quantity"
            label="Quantity"
            rules={[{ required: true, message: 'Please enter quantity' }]}
          >
            <Input type="number" min={1} placeholder="1" />
          </Form.Item>
          <Form.Item name="notes" label="Notes">
            <TextArea rows={2} placeholder="Optional notes..." />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
