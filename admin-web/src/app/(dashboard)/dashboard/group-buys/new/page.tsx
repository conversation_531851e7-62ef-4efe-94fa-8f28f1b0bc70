'use client';

import { useRouter } from 'next/navigation';
import { Button, Typography } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { GroupBuyForm } from '@/components/forms/GroupBuyForm';
import { GroupBuy } from '@/types';

const { Title } = Typography;

export default function NewGroupBuyPage() {
  const router = useRouter();

  const handleSuccess = (groupBuy: GroupBuy) => {
    router.push(`/dashboard/group-buys/${groupBuy.id}`);
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => router.back()}
          >
            Back
          </Button>
          <Title level={3} className="!mb-0">
            Create New Group Buy
          </Title>
        </div>
      </div>

      {/* Group Buy Form */}
      <GroupBuyForm
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
}
