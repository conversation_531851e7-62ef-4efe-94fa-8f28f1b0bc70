'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Progress,
  Tooltip,
  Dropdown,
  DatePicker,
  Row,
  Col,
  Statistic,
  Typography,
  Badge,
  Image,
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  MoreOutlined,
  DownloadOutlined,
  ReloadOutlined,
  EyeOutlined,
  EditOutlined,
  TeamOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  TrophyOutlined,
  FireOutlined,
} from '@ant-design/icons';
import {
  useGroupBuys,
  useGroupBuyStats,
  useExportGroupBuys,
  useBulkUpdateGroupBuys,
  useUpdateGroupBuyStatus,
} from '@/lib/hooks/useGroupBuys';
import { GroupBuyWithDetails, GroupBuyFilters } from '@/lib/api/groupbuys';
import { StatusBadge } from '@/components/ui/StatusBadge';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { formatDate, formatCurrency, formatRelativeTime, formatGroupBuyProgress } from '@/lib/utils/formatters';
import { PAGINATION_DEFAULTS, GROUP_BUY_STATUSES } from '@/constants';

const { Title } = Typography;
const { RangePicker } = DatePicker;

export default function GroupBuysPage() {
  const router = useRouter();
  const [filters, setFilters] = useState<GroupBuyFilters>({
    page: PAGINATION_DEFAULTS.PAGE,
    limit: PAGINATION_DEFAULTS.LIMIT,
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  const { data: groupBuysData, isLoading, refetch } = useGroupBuys(filters);
  const { data: statsData } = useGroupBuyStats();
  const exportGroupBuys = useExportGroupBuys();
  const bulkUpdateGroupBuys = useBulkUpdateGroupBuys();
  const updateGroupBuyStatus = useUpdateGroupBuyStatus();

  const handleTableChange = (pagination: any, tableFilters: any, sorter: any) => {
    setFilters(prev => ({
      ...prev,
      page: pagination.current,
      limit: pagination.pageSize,
    }));
  };

  const handleSearch = (value: string) => {
    setFilters(prev => ({
      ...prev,
      search: value || undefined,
      page: 1,
    }));
  };

  const handleFilterChange = (key: keyof GroupBuyFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1,
    }));
  };

  const handleExport = () => {
    exportGroupBuys.mutate(filters);
  };

  const handleBulkStatusUpdate = (status: string) => {
    if (selectedRowKeys.length === 0) return;
    
    bulkUpdateGroupBuys.mutate({
      groupBuyIds: selectedRowKeys,
      data: { status },
    });
    setSelectedRowKeys([]);
  };

  const handleViewGroupBuy = (groupBuyId: string) => {
    router.push(`/dashboard/group-buys/${groupBuyId}`);
  };

  const handleQuickStatusUpdate = (groupBuyId: string, status: string) => {
    updateGroupBuyStatus.mutate({ groupBuyId, status });
  };

  const getTimeRemainingColor = (timeRemaining: number) => {
    const hoursRemaining = timeRemaining / (1000 * 60 * 60);
    if (hoursRemaining < 24) return 'red';
    if (hoursRemaining < 72) return 'orange';
    return 'green';
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 100) return '#52c41a';
    if (percentage >= 75) return '#1890ff';
    if (percentage >= 50) return '#fa8c16';
    return '#f5222d';
  };

  const getGroupBuyActions = (groupBuy: GroupBuyWithDetails) => [
    {
      key: 'view',
      label: 'View Details',
      icon: <EyeOutlined />,
      onClick: () => handleViewGroupBuy(groupBuy.id),
    },
    {
      key: 'edit',
      label: 'Edit Group Buy',
      icon: <EditOutlined />,
      onClick: () => router.push(`/dashboard/group-buys/${groupBuy.id}/edit`),
    },
    {
      key: 'participants',
      label: 'Manage Participants',
      icon: <TeamOutlined />,
      onClick: () => router.push(`/dashboard/group-buys/${groupBuy.id}/participants`),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'successful',
      label: 'Mark Successful',
      disabled: groupBuy.status === 'Successful',
      onClick: () => handleQuickStatusUpdate(groupBuy.id, 'Successful'),
    },
    {
      key: 'fulfilled',
      label: 'Mark Fulfilled',
      disabled: groupBuy.status === 'Fulfilled',
      onClick: () => handleQuickStatusUpdate(groupBuy.id, 'Fulfilled'),
    },
    {
      key: 'failed',
      label: 'Mark Failed',
      disabled: groupBuy.status === 'Failed',
      danger: true,
      onClick: () => handleQuickStatusUpdate(groupBuy.id, 'Failed'),
    },
  ];

  const columns = [
    {
      title: 'Product',
      key: 'product',
      render: (record: GroupBuyWithDetails) => (
        <div className="flex items-center space-x-3">
          <Image
            src={record.productVariant.mainImageUrl}
            alt={record.productVariant.name}
            width={60}
            height={60}
            className="rounded-lg object-cover"
            fallback="/placeholder-product.png"
          />
          <div>
            <div className="font-medium text-blue-600 cursor-pointer hover:text-blue-800 max-w-xs truncate"
                 onClick={() => handleViewGroupBuy(record.id)}>
              {record.productVariant.product.name}
            </div>
            <div className="text-sm text-gray-500">{record.productVariant.name}</div>
            <div className="text-xs text-gray-400">SKU: {record.productVariant.sku}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: GroupBuyWithDetails) => (
        <div className="space-y-1">
          <StatusBadge status={status} type="groupbuy" />
          {record.timeRemaining > 0 && status === 'Active' && (
            <div className="text-xs" style={{ color: getTimeRemainingColor(record.timeRemaining) }}>
              {Math.ceil(record.timeRemaining / (1000 * 60 * 60))}h remaining
            </div>
          )}
        </div>
      ),
      filters: GROUP_BUY_STATUSES.map(status => ({ text: status, value: status })),
      onFilter: (value: any, record: GroupBuyWithDetails) => record.status === value,
    },
    {
      title: 'Progress',
      key: 'progress',
      render: (record: GroupBuyWithDetails) => (
        <div className="space-y-2">
          <Progress
            percent={record.progressPercentage}
            size="small"
            strokeColor={getProgressColor(record.progressPercentage)}
            format={() => `${record.currentQuantity}/${record.targetQuantity}`}
          />
          <div className="text-xs text-gray-500">
            {record.participantCount} participants
          </div>
        </div>
      ),
      sorter: (a: GroupBuyWithDetails, b: GroupBuyWithDetails) => a.progressPercentage - b.progressPercentage,
    },
    {
      title: 'Pricing',
      key: 'pricing',
      render: (record: GroupBuyWithDetails) => (
        <div className="space-y-1">
          <div className="font-medium text-green-600">
            {formatCurrency(record.groupPrice, record.productVariant.currency)}
          </div>
          <div className="text-xs text-gray-500 line-through">
            {formatCurrency(record.productVariant.myPrice, record.productVariant.currency)}
          </div>
          <div className="text-xs text-orange-600">
            Save {formatCurrency(record.estimatedSavings, record.productVariant.currency)}
          </div>
        </div>
      ),
      sorter: (a: GroupBuyWithDetails, b: GroupBuyWithDetails) => a.groupPrice - b.groupPrice,
    },
    {
      title: 'Total Value',
      dataIndex: 'totalValue',
      key: 'totalValue',
      render: (value: number, record: GroupBuyWithDetails) => (
        <div className="font-medium">
          {formatCurrency(value, record.productVariant.currency)}
        </div>
      ),
      sorter: (a: GroupBuyWithDetails, b: GroupBuyWithDetails) => a.totalValue - b.totalValue,
    },
    {
      title: 'Expires',
      dataIndex: 'expiresAt',
      key: 'expiresAt',
      render: (date: Date, record: GroupBuyWithDetails) => (
        <div className="space-y-1">
          <Tooltip title={formatDate(date)}>
            <div className="text-sm">{formatDate(date, 'MMM DD')}</div>
          </Tooltip>
          {record.timeRemaining > 0 && record.status === 'Active' && (
            <div className="text-xs" style={{ color: getTimeRemainingColor(record.timeRemaining) }}>
              {formatRelativeTime(date)}
            </div>
          )}
        </div>
      ),
      sorter: (a: GroupBuyWithDetails, b: GroupBuyWithDetails) => 
        new Date(a.expiresAt).getTime() - new Date(b.expiresAt).getTime(),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 80,
      render: (record: GroupBuyWithDetails) => (
        <Dropdown
          menu={{ items: getGroupBuyActions(record) }}
          trigger={['click']}
          placement="bottomRight"
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: React.Key[]) => setSelectedRowKeys(keys as string[]),
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Title level={2} className="!mb-0">
          Group Buy Management
        </Title>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => refetch()}
            loading={isLoading}
          >
            Refresh
          </Button>
          <Button
            icon={<DownloadOutlined />}
            onClick={handleExport}
            loading={exportGroupBuys.isPending}
          >
            Export
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => router.push('/dashboard/group-buys/new')}
          >
            Create Group Buy
          </Button>
        </Space>
      </div>

      {/* Statistics */}
      {statsData && (
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Group Buys"
                value={statsData.data.totalGroupBuys}
                prefix={<TeamOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Active Group Buys"
                value={statsData.data.activeGroupBuys}
                prefix={<FireOutlined />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Success Rate"
                value={statsData.data.successRate}
                suffix="%"
                prefix={<TrophyOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Participants"
                value={statsData.data.totalParticipants}
                prefix={<TeamOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* Filters */}
      <Card>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={6}>
            <Input
              placeholder="Search group buys..."
              prefix={<SearchOutlined />}
              allowClear
              onChange={(e) => handleSearch(e.target.value)}
              value={filters.search}
            />
          </Col>
          <Col xs={24} sm={12} md={4}>
            <Select
              placeholder="Status"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleFilterChange('status', value)}
              value={filters.status}
            >
              {GROUP_BUY_STATUSES.map(status => (
                <Select.Option key={status} value={status}>
                  {status}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <RangePicker
              style={{ width: '100%' }}
              onChange={(dates) => {
                handleFilterChange('dateFrom', dates?.[0]?.toDate());
                handleFilterChange('dateTo', dates?.[1]?.toDate());
              }}
              placeholder={['Start Date', 'End Date']}
            />
          </Col>
          <Col xs={24} sm={12} md={3}>
            <Button
              block
              onClick={() => setFilters({
                page: PAGINATION_DEFAULTS.PAGE,
                limit: PAGINATION_DEFAULTS.LIMIT,
              })}
            >
              Clear Filters
            </Button>
          </Col>
          <Col xs={24} sm={12} md={3}>
            {selectedRowKeys.length > 0 && (
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'successful',
                      label: 'Mark Successful',
                      onClick: () => handleBulkStatusUpdate('Successful'),
                    },
                    {
                      key: 'fulfilled',
                      label: 'Mark Fulfilled',
                      onClick: () => handleBulkStatusUpdate('Fulfilled'),
                    },
                    {
                      key: 'failed',
                      label: 'Mark Failed',
                      onClick: () => handleBulkStatusUpdate('Failed'),
                    },
                  ],
                }}
                trigger={['click']}
              >
                <Button block>
                  Bulk Actions ({selectedRowKeys.length})
                </Button>
              </Dropdown>
            )}
          </Col>
        </Row>
      </Card>

      {/* Group Buys Table */}
      <Card>
        {isLoading ? (
          <LoadingSpinner />
        ) : (
          <Table
            columns={columns}
            dataSource={groupBuysData?.data.items || []}
            rowKey="id"
            rowSelection={rowSelection}
            pagination={{
              current: filters.page,
              pageSize: filters.limit,
              total: groupBuysData?.data.total || 0,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} of ${total} group buys`,
            }}
            onChange={handleTableChange}
            scroll={{ x: 1400 }}
          />
        )}
      </Card>
    </div>
  );
}
