'use client';

import { useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import {
  Card,
  Descriptions,
  Button,
  Space,
  Tag,
  Tabs,
  Table,
  Typography,
  Row,
  Col,
  Timeline,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Steps,
  Alert,
  Tooltip,
  Dropdown,
  Progress,
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  UserOutlined,
  ShoppingCartOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  DollarOutlined,
  TruckOutlined,
  MoreOutlined,
  IssuesCloseOutlined,
  CreditCardOutlined,
  HistoryOutlined,
} from '@ant-design/icons';
import {
  useOrder,
  useOrderTimeline,
  useOrderFulfillmentSummary,
  useUpdateOrderStatus,
  useCreateOrderIssue,
  useUpdateItemFulfillmentStatus,
  useAddTrackingInfo,
  useCancelOrder,
  useProcessRefund,
} from '@/lib/hooks/useOrders';
import { StatusBadge } from '@/components/ui/StatusBadge';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { formatDate, formatCurrency, formatAddress } from '@/lib/utils/formatters';
import { OrderItem, OrderIssue } from '@/types';
import { ORDER_ISSUE_TYPES, FULFILLMENT_STATUSES } from '@/constants';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Step } = Steps;

export default function OrderDetailPage() {
  const params = useParams();
  const router = useRouter();
  const orderId = params.id as string;

  const [statusModalVisible, setStatusModalVisible] = useState(false);
  const [issueModalVisible, setIssueModalVisible] = useState(false);
  const [trackingModalVisible, setTrackingModalVisible] = useState(false);
  const [refundModalVisible, setRefundModalVisible] = useState(false);
  const [selectedOrderItem, setSelectedOrderItem] = useState<OrderItem | null>(null);

  const [statusForm] = Form.useForm();
  const [issueForm] = Form.useForm();
  const [trackingForm] = Form.useForm();
  const [refundForm] = Form.useForm();

  const { data: orderData, isLoading: orderLoading } = useOrder(orderId);
  const { data: timelineData } = useOrderTimeline(orderId);
  const { data: fulfillmentData } = useOrderFulfillmentSummary(orderId);

  const updateOrderStatus = useUpdateOrderStatus();
  const createOrderIssue = useCreateOrderIssue();
  const updateItemFulfillmentStatus = useUpdateItemFulfillmentStatus();
  const addTrackingInfo = useAddTrackingInfo();
  const cancelOrder = useCancelOrder();
  const processRefund = useProcessRefund();

  if (orderLoading) {
    return <LoadingSpinner />;
  }

  if (!orderData?.data) {
    return (
      <div className="text-center py-8">
        <Text type="secondary">Order not found</Text>
      </div>
    );
  }

  const order = orderData.data;

  const handleUpdateStatus = async (values: any) => {
    try {
      await updateOrderStatus.mutateAsync({
        orderId,
        status: values.status,
        notes: values.notes,
      });
      setStatusModalVisible(false);
      statusForm.resetFields();
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleCreateIssue = async (values: any) => {
    if (!selectedOrderItem) return;
    
    try {
      await createOrderIssue.mutateAsync({
        orderId,
        data: {
          orderItemId: selectedOrderItem.id,
          issueType: values.issueType,
          details: values.details,
          priceDifference: values.priceDifference,
        },
      });
      setIssueModalVisible(false);
      issueForm.resetFields();
      setSelectedOrderItem(null);
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleAddTracking = async (values: any) => {
    try {
      await addTrackingInfo.mutateAsync({
        orderId,
        data: values,
      });
      setTrackingModalVisible(false);
      trackingForm.resetFields();
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleProcessRefund = async (values: any) => {
    try {
      await processRefund.mutateAsync({
        orderId,
        ...values,
      });
      setRefundModalVisible(false);
      refundForm.resetFields();
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleCancelOrder = () => {
    Modal.confirm({
      title: 'Cancel Order',
      content: 'Are you sure you want to cancel this order? This action cannot be undone.',
      onOk: () => {
        Modal.confirm({
          title: 'Cancellation Reason',
          content: (
            <Input.TextArea
              placeholder="Please provide a reason for cancellation..."
              rows={3}
              onChange={(e) => {
                // Store reason in a ref or state
              }}
            />
          ),
          onOk: () => {
            cancelOrder.mutate({ orderId, reason: 'Admin cancellation' });
          },
        });
      },
    });
  };

  const getOrderProgress = () => {
    const statusOrder = ['Pending Payment', 'Processing', 'Shipped', 'Delivered'];
    const currentIndex = statusOrder.indexOf(order.status);
    return {
      current: currentIndex >= 0 ? currentIndex : 0,
      status: order.status === 'Cancelled' ? 'error' : 
              order.status === 'Action Required' ? 'exception' : 'normal',
    };
  };

  const getItemActions = (item: OrderItem) => [
    {
      key: 'fulfillment',
      label: 'Update Fulfillment',
      onClick: () => {
        // Handle fulfillment update
      },
    },
    {
      key: 'issue',
      label: 'Create Issue',
      onClick: () => {
        setSelectedOrderItem(item);
        setIssueModalVisible(true);
      },
    },
  ];

  const itemColumns = [
    {
      title: 'Product',
      key: 'product',
      render: (record: OrderItem) => (
        <div className="flex items-center space-x-3">
          <img
            src={record.productSnapshot.image}
            alt={record.productSnapshot.name}
            className="w-12 h-12 rounded object-cover"
          />
          <div>
            <div className="font-medium">{record.productSnapshot.name}</div>
            <div className="text-sm text-gray-500">SKU: {record.productSnapshot.sku}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity: number) => <Tag color="blue">{quantity}</Tag>,
    },
    {
      title: 'Price',
      dataIndex: 'pricePerUnitPaid',
      key: 'pricePerUnitPaid',
      render: (price: number) => formatCurrency(price),
    },
    {
      title: 'Type',
      key: 'type',
      render: (record: OrderItem) => (
        <Tag color={record.isGroupBuy ? 'purple' : 'default'}>
          {record.isGroupBuy ? 'Group Buy' : 'Regular'}
        </Tag>
      ),
    },
    {
      title: 'Fulfillment',
      dataIndex: 'fulfillmentStatus',
      key: 'fulfillmentStatus',
      render: (status: string) => <StatusBadge status={status} />,
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: OrderItem) => (
        <Dropdown
          menu={{ items: getItemActions(record) }}
          trigger={['click']}
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  const issueColumns = [
    {
      title: 'Type',
      dataIndex: 'issueType',
      key: 'issueType',
      render: (type: string) => <Tag color="red">{type}</Tag>,
    },
    {
      title: 'Details',
      dataIndex: 'details',
      key: 'details',
      render: (details: string) => (
        <Tooltip title={details}>
          <Text className="max-w-xs truncate">{details}</Text>
        </Tooltip>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => <StatusBadge status={status} />,
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: Date) => formatDate(date),
    },
  ];

  const progress = getOrderProgress();

  const tabItems = [
    {
      key: 'overview',
      label: 'Overview',
      children: (
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card title="Order Information">
              <Descriptions column={1} size="small">
                <Descriptions.Item label="Order Number">
                  {order.orderNumber}
                </Descriptions.Item>
                <Descriptions.Item label="Status">
                  <StatusBadge status={order.status} type="order" />
                </Descriptions.Item>
                <Descriptions.Item label="Total Amount">
                  {formatCurrency(order.totalAmount, order.currency)}
                </Descriptions.Item>
                <Descriptions.Item label="Items">
                  {order.itemCount} items
                </Descriptions.Item>
                <Descriptions.Item label="Created">
                  {formatDate(order.createdAt)}
                </Descriptions.Item>
              </Descriptions>
            </Card>

            <Card title="Customer Information" className="mt-4">
              <Descriptions column={1} size="small">
                <Descriptions.Item label="Name">
                  {order.user.displayName}
                </Descriptions.Item>
                <Descriptions.Item label="Email">
                  {order.user.email}
                </Descriptions.Item>
                <Descriptions.Item label="Shipping Address">
                  {formatAddress(order.shippingAddress)}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="Order Progress">
              <Steps
                current={progress.current}
                status={progress.status as any}
                direction="vertical"
                size="small"
              >
                <Step title="Payment" description="Order payment received" />
                <Step title="Processing" description="Order being processed" />
                <Step title="Shipped" description="Order shipped to customer" />
                <Step title="Delivered" description="Order delivered" />
              </Steps>
            </Card>

            {fulfillmentData && (
              <Card title="Fulfillment Summary" className="mt-4">
                <Row gutter={16}>
                  <Col span={12}>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {fulfillmentData.data.pendingItems}
                      </div>
                      <div className="text-sm text-gray-500">Pending</div>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {fulfillmentData.data.shippedItems}
                      </div>
                      <div className="text-sm text-gray-500">Shipped</div>
                    </div>
                  </Col>
                </Row>
                <Progress
                  percent={Math.round((fulfillmentData.data.shippedItems / fulfillmentData.data.totalItems) * 100)}
                  className="mt-4"
                />
              </Card>
            )}
          </Col>
        </Row>
      ),
    },
    {
      key: 'items',
      label: `Items (${order.itemCount})`,
      icon: <ShoppingCartOutlined />,
      children: (
        <Card>
          <Table
            columns={itemColumns}
            dataSource={order.items || []}
            rowKey="id"
            pagination={false}
            scroll={{ x: 800 }}
          />
        </Card>
      ),
    },
    {
      key: 'issues',
      label: `Issues (${order.issueCount})`,
      icon: <ExclamationCircleOutlined />,
      children: (
        <Card
          title="Order Issues"
          extra={
            <Button
              type="primary"
              icon={<IssuesCloseOutlined />}
              onClick={() => setIssueModalVisible(true)}
            >
              Create Issue
            </Button>
          }
        >
          <Table
            columns={issueColumns}
            dataSource={order.issues || []}
            rowKey="id"
            pagination={false}
            locale={{ emptyText: 'No issues found' }}
          />
        </Card>
      ),
    },
    {
      key: 'timeline',
      label: 'Timeline',
      icon: <HistoryOutlined />,
      children: (
        <Card title="Order Timeline">
          <Timeline>
            {timelineData?.data.map((event) => (
              <Timeline.Item
                key={event.id}
                dot={getOrderProgress().status === 'error' ? <ExclamationCircleOutlined /> : <CheckCircleOutlined />}
              >
                <div className="space-y-1">
                  <div className="font-medium">{event.status}</div>
                  <div className="text-sm text-gray-600">{event.description}</div>
                  <div className="text-xs text-gray-500">
                    {formatDate(event.timestamp)}
                    {event.adminName && ` by ${event.adminName}`}
                  </div>
                </div>
              </Timeline.Item>
            ))}
          </Timeline>
        </Card>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => router.back()}
          >
            Back
          </Button>
          <div>
            <Title level={3} className="!mb-0">
              Order {order.orderNumber}
            </Title>
            <Text type="secondary">
              {order.user.displayName} • {formatDate(order.createdAt)}
            </Text>
          </div>
        </div>

        <Space>
          <Button
            icon={<EditOutlined />}
            onClick={() => setStatusModalVisible(true)}
          >
            Update Status
          </Button>
          <Button
            icon={<TruckOutlined />}
            onClick={() => setTrackingModalVisible(true)}
          >
            Add Tracking
          </Button>
          <Button
            icon={<DollarOutlined />}
            onClick={() => setRefundModalVisible(true)}
          >
            Process Refund
          </Button>
          <Button
            danger
            onClick={handleCancelOrder}
            disabled={order.status === 'Cancelled' || order.status === 'Delivered'}
          >
            Cancel Order
          </Button>
        </Space>
      </div>

      {/* Alert for Action Required */}
      {order.status === 'Action Required' && (
        <Alert
          message="Action Required"
          description="This order has issues that require immediate attention."
          type="error"
          showIcon
          action={
            <Button size="small" danger>
              View Issues
            </Button>
          }
        />
      )}

      {/* Content Tabs */}
      <Tabs items={tabItems} />

      {/* Status Update Modal */}
      <Modal
        title="Update Order Status"
        open={statusModalVisible}
        onCancel={() => setStatusModalVisible(false)}
        onOk={() => statusForm.submit()}
        confirmLoading={updateOrderStatus.isPending}
      >
        <Form
          form={statusForm}
          layout="vertical"
          onFinish={handleUpdateStatus}
        >
          <Form.Item
            name="status"
            label="New Status"
            rules={[{ required: true, message: 'Please select status' }]}
          >
            <Select>
              <Select.Option value="Processing">Processing</Select.Option>
              <Select.Option value="Shipped">Shipped</Select.Option>
              <Select.Option value="Delivered">Delivered</Select.Option>
              <Select.Option value="Cancelled">Cancelled</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item name="notes" label="Notes">
            <TextArea rows={3} placeholder="Optional notes..." />
          </Form.Item>
        </Form>
      </Modal>

      {/* Create Issue Modal */}
      <Modal
        title="Create Order Issue"
        open={issueModalVisible}
        onCancel={() => {
          setIssueModalVisible(false);
          setSelectedOrderItem(null);
        }}
        onOk={() => issueForm.submit()}
        confirmLoading={createOrderIssue.isPending}
      >
        <Form
          form={issueForm}
          layout="vertical"
          onFinish={handleCreateIssue}
        >
          <Form.Item
            name="issueType"
            label="Issue Type"
            rules={[{ required: true, message: 'Please select issue type' }]}
          >
            <Select>
              {ORDER_ISSUE_TYPES.map(type => (
                <Select.Option key={type} value={type}>
                  {type}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="details"
            label="Details"
            rules={[{ required: true, message: 'Please enter details' }]}
          >
            <TextArea rows={4} placeholder="Describe the issue..." />
          </Form.Item>
          <Form.Item name="priceDifference" label="Price Difference (if applicable)">
            <InputNumber
              style={{ width: '100%' }}
              placeholder="0.00"
              precision={2}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* Add Tracking Modal */}
      <Modal
        title="Add Tracking Information"
        open={trackingModalVisible}
        onCancel={() => setTrackingModalVisible(false)}
        onOk={() => trackingForm.submit()}
        confirmLoading={addTrackingInfo.isPending}
      >
        <Form
          form={trackingForm}
          layout="vertical"
          onFinish={handleAddTracking}
        >
          <Form.Item
            name="trackingNumber"
            label="Tracking Number"
            rules={[{ required: true, message: 'Please enter tracking number' }]}
          >
            <Input placeholder="Enter tracking number" />
          </Form.Item>
          <Form.Item
            name="carrier"
            label="Carrier"
            rules={[{ required: true, message: 'Please enter carrier' }]}
          >
            <Input placeholder="e.g., DHL, FedEx, UPS" />
          </Form.Item>
          <Form.Item name="trackingUrl" label="Tracking URL">
            <Input placeholder="https://..." />
          </Form.Item>
        </Form>
      </Modal>

      {/* Process Refund Modal */}
      <Modal
        title="Process Refund"
        open={refundModalVisible}
        onCancel={() => setRefundModalVisible(false)}
        onOk={() => refundForm.submit()}
        confirmLoading={processRefund.isPending}
      >
        <Form
          form={refundForm}
          layout="vertical"
          onFinish={handleProcessRefund}
        >
          <Form.Item
            name="refundAmount"
            label="Refund Amount"
            rules={[{ required: true, message: 'Please enter refund amount' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="0.00"
              precision={2}
              max={order.totalAmount}
            />
          </Form.Item>
          <Form.Item
            name="reason"
            label="Reason"
            rules={[{ required: true, message: 'Please enter reason' }]}
          >
            <TextArea rows={3} placeholder="Reason for refund..." />
          </Form.Item>
          <Form.Item name="notes" label="Notes">
            <TextArea rows={2} placeholder="Additional notes..." />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
