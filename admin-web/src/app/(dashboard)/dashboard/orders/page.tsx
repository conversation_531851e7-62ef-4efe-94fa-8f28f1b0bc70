'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Tooltip,
  Dropdown,
  DatePicker,
  Row,
  Col,
  Statistic,
  Typography,
  Badge,
  Avatar,
} from 'antd';
import {
  SearchOutlined,
  MoreOutlined,
  DownloadOutlined,
  ReloadOutlined,
  EyeOutlined,
  EditOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  useOrders,
  useOrderStats,
  useExportOrders,
  useBulkUpdateOrders,
  useUpdateOrderStatus,
} from '@/lib/hooks/useOrders';
import { OrderFilters } from '@/types';
import { OrderWithDetails } from '@/lib/api/orders';
import { StatusBadge } from '@/components/ui/StatusBadge';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { formatDate, formatCurrency, formatRelativeTime } from '@/lib/utils/formatters';
import { PAGINATION_DEFAULTS, ORDER_STATUSES } from '@/constants';

const { Title } = Typography;
const { RangePicker } = DatePicker;

export default function OrdersPage() {
  const router = useRouter();
  const [filters, setFilters] = useState<OrderFilters>({
    page: PAGINATION_DEFAULTS.PAGE,
    limit: PAGINATION_DEFAULTS.LIMIT,
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  const { data: ordersData, isLoading, refetch } = useOrders(filters);
  const { data: statsData } = useOrderStats();
  const exportOrders = useExportOrders();
  const bulkUpdateOrders = useBulkUpdateOrders();
  const updateOrderStatus = useUpdateOrderStatus();

  const handleTableChange = (pagination: any, tableFilters: any, sorter: any) => {
    setFilters(prev => ({
      ...prev,
      page: pagination.current,
      limit: pagination.pageSize,
    }));
  };

  const handleSearch = (value: string) => {
    setFilters(prev => ({
      ...prev,
      search: value || undefined,
      page: 1,
    }));
  };

  const handleFilterChange = (key: keyof OrderFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1,
    }));
  };

  const handleExport = () => {
    exportOrders.mutate(filters);
  };

  const handleBulkStatusUpdate = (status: string) => {
    if (selectedRowKeys.length === 0) return;
    
    bulkUpdateOrders.mutate({
      orderIds: selectedRowKeys,
      data: { status },
    });
    setSelectedRowKeys([]);
  };

  const handleViewOrder = (orderId: string) => {
    router.push(`/dashboard/orders/${orderId}`);
  };

  const handleQuickStatusUpdate = (orderId: string, status: string) => {
    updateOrderStatus.mutate({ orderId, status });
  };

  const getOrderActions = (order: OrderWithDetails) => [
    {
      key: 'view',
      label: 'View Details',
      icon: <EyeOutlined />,
      onClick: () => handleViewOrder(order.id),
    },
    {
      key: 'edit',
      label: 'Edit Order',
      icon: <EditOutlined />,
      onClick: () => router.push(`/dashboard/orders/${order.id}/edit`),
    },
    {
      key: 'customer',
      label: 'View Customer',
      icon: <UserOutlined />,
      onClick: () => router.push(`/dashboard/users/${order.userId}`),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'processing',
      label: 'Mark Processing',
      disabled: order.status === 'Processing',
      onClick: () => handleQuickStatusUpdate(order.id, 'Processing'),
    },
    {
      key: 'shipped',
      label: 'Mark Shipped',
      disabled: order.status === 'Shipped',
      onClick: () => handleQuickStatusUpdate(order.id, 'Shipped'),
    },
    {
      key: 'delivered',
      label: 'Mark Delivered',
      disabled: order.status === 'Delivered',
      onClick: () => handleQuickStatusUpdate(order.id, 'Delivered'),
    },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Pending Payment':
        return <ClockCircleOutlined className="text-orange-500" />;
      case 'Processing':
        return <ClockCircleOutlined className="text-blue-500" />;
      case 'Action Required':
        return <ExclamationCircleOutlined className="text-red-500" />;
      case 'Shipped':
        return <CheckCircleOutlined className="text-green-500" />;
      case 'Delivered':
        return <CheckCircleOutlined className="text-green-600" />;
      default:
        return <ClockCircleOutlined className="text-gray-500" />;
    }
  };

  const columns = [
    {
      title: 'Order',
      key: 'order',
      render: (record: OrderWithDetails) => (
        <div className="space-y-1">
          <div className="font-medium text-blue-600 cursor-pointer hover:text-blue-800"
               onClick={() => handleViewOrder(record.id)}>
            {record.orderNumber}
          </div>
          <div className="text-xs text-gray-500">
            {formatDate(record.createdAt)}
          </div>
        </div>
      ),
    },
    {
      title: 'Customer',
      key: 'customer',
      render: (record: OrderWithDetails) => (
        <div className="flex items-center space-x-2">
          <Avatar size="small" icon={<UserOutlined />} />
          <div>
            <div className="font-medium text-sm">{record.user.displayName}</div>
            <div className="text-xs text-gray-500">{record.user.email}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: OrderWithDetails) => (
        <div className="flex items-center space-x-2">
          {getStatusIcon(status)}
          <StatusBadge status={status} type="order" />
          {record.issueCount > 0 && (
            <Badge count={record.issueCount} size="small" />
          )}
        </div>
      ),
      filters: ORDER_STATUSES.map(status => ({ text: status, value: status })),
      onFilter: (value: any, record: OrderWithDetails) => record.status === value,
    },
    {
      title: 'Items',
      dataIndex: 'itemCount',
      key: 'itemCount',
      render: (count: number) => (
        <Tag color="blue">{count} items</Tag>
      ),
      sorter: (a: OrderWithDetails, b: OrderWithDetails) => a.itemCount - b.itemCount,
    },
    {
      title: 'Total',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (amount: number, record: OrderWithDetails) => (
        <div className="font-medium">
          {formatCurrency(amount, record.currency)}
        </div>
      ),
      sorter: (a: OrderWithDetails, b: OrderWithDetails) => a.totalAmount - b.totalAmount,
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: Date) => (
        <Tooltip title={formatDate(date)}>
          {formatRelativeTime(date)}
        </Tooltip>
      ),
      sorter: (a: OrderWithDetails, b: OrderWithDetails) => 
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 80,
      render: (record: OrderWithDetails) => (
        <Dropdown
          menu={{ items: getOrderActions(record) }}
          trigger={['click']}
          placement="bottomRight"
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: React.Key[]) => setSelectedRowKeys(keys as string[]),
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Title level={2} className="!mb-0">
          Order Management
        </Title>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => refetch()}
            loading={isLoading}
          >
            Refresh
          </Button>
          <Button
            icon={<DownloadOutlined />}
            onClick={handleExport}
            loading={exportOrders.isPending}
          >
            Export
          </Button>
        </Space>
      </div>

      {/* Statistics */}
      {statsData && (
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Orders"
                value={statsData.data.totalOrders}
                prefix={<ShoppingCartOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Pending Orders"
                value={statsData.data.pendingOrders}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Action Required"
                value={statsData.data.actionRequiredOrders}
                prefix={<ExclamationCircleOutlined />}
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Revenue"
                value={statsData.data.totalRevenue}
                prefix={<DollarOutlined />}
                formatter={(value) => formatCurrency(Number(value))}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* Filters */}
      <Card>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={6}>
            <Input
              placeholder="Search orders..."
              prefix={<SearchOutlined />}
              allowClear
              onChange={(e) => handleSearch(e.target.value)}
              value={filters.search}
            />
          </Col>
          <Col xs={24} sm={12} md={4}>
            <Select
              placeholder="Status"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleFilterChange('status', value)}
              value={filters.status}
            >
              {ORDER_STATUSES.map(status => (
                <Select.Option key={status} value={status}>
                  {status}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <RangePicker
              style={{ width: '100%' }}
              onChange={(dates) => {
                handleFilterChange('dateFrom', dates?.[0]?.toDate());
                handleFilterChange('dateTo', dates?.[1]?.toDate());
              }}
              placeholder={['Start Date', 'End Date']}
            />
          </Col>
          <Col xs={24} sm={12} md={3}>
            <Button
              block
              onClick={() => setFilters({
                page: PAGINATION_DEFAULTS.PAGE,
                limit: PAGINATION_DEFAULTS.LIMIT,
              })}
            >
              Clear Filters
            </Button>
          </Col>
          <Col xs={24} sm={12} md={3}>
            {selectedRowKeys.length > 0 && (
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'processing',
                      label: 'Mark Processing',
                      onClick: () => handleBulkStatusUpdate('Processing'),
                    },
                    {
                      key: 'shipped',
                      label: 'Mark Shipped',
                      onClick: () => handleBulkStatusUpdate('Shipped'),
                    },
                    {
                      key: 'delivered',
                      label: 'Mark Delivered',
                      onClick: () => handleBulkStatusUpdate('Delivered'),
                    },
                  ],
                }}
                trigger={['click']}
              >
                <Button block>
                  Bulk Actions ({selectedRowKeys.length})
                </Button>
              </Dropdown>
            )}
          </Col>
        </Row>
      </Card>

      {/* Orders Table */}
      <Card>
        {isLoading ? (
          <LoadingSpinner />
        ) : (
          <Table
            columns={columns}
            dataSource={ordersData?.data.items || []}
            rowKey="id"
            rowSelection={rowSelection}
            pagination={{
              current: filters.page,
              pageSize: filters.limit,
              total: ordersData?.data.total || 0,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} of ${total} orders`,
            }}
            onChange={handleTableChange}
            scroll={{ x: 1200 }}
          />
        )}
      </Card>
    </div>
  );
}
