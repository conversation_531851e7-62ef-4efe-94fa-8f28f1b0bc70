'use client';

import { useParams, useRouter } from 'next/navigation';
import { Button, Typography } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useProduct } from '@/lib/hooks/useProducts';
import { ProductForm } from '@/components/forms/ProductForm';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Product } from '@/types';

const { Title, Text } = Typography;

export default function EditProductPage() {
  const params = useParams();
  const router = useRouter();
  const productId = params.id as string;

  const { data: productData, isLoading } = useProduct(productId);

  const handleSuccess = (product: Product) => {
    router.push(`/dashboard/products/${product.id}`);
  };

  const handleCancel = () => {
    router.back();
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!productData?.data) {
    return (
      <div className="text-center py-8">
        <Text type="secondary">Product not found</Text>
      </div>
    );
  }

  const product = productData.data;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => router.back()}
          >
            Back
          </Button>
          <div>
            <Title level={3} className="!mb-0">
              Edit Product
            </Title>
            <Text type="secondary">{product.name}</Text>
          </div>
        </div>
      </div>

      {/* Product Form */}
      <ProductForm
        product={product}
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
}
