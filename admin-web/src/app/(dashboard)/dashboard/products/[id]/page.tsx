'use client';

import { useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import {
  Card,
  Descriptions,
  Button,
  Space,
  Tag,
  Tabs,
  Table,
  Typography,
  Row,
  Col,
  Image,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Upload,
  message,
  Tooltip,
  Dropdown,
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  PlusOutlined,
  UploadOutlined,
  MoreOutlined,
  EyeOutlined,
  DeleteOutlined,
  LinkOutlined,
  ShoppingOutlined,
  PictureOutlined,
} from '@ant-design/icons';
import {
  useProduct,
  useProductVariants,
  useCreateProductVariant,
  useUpdateProductVariant,
  useDeleteProductVariant,
  useUploadProductImages,
} from '@/lib/hooks/useProducts';
import { StatusBadge } from '@/components/ui/StatusBadge';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { formatDate, formatCurrency, formatWeight } from '@/lib/utils/formatters';
import { ProductVariant } from '@/types';
import { WEIGHT_UNITS, CURRENCIES } from '@/constants';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

export default function ProductDetailPage() {
  const params = useParams();
  const router = useRouter();
  const productId = params.id as string;

  const [variantModalVisible, setVariantModalVisible] = useState(false);
  const [editingVariant, setEditingVariant] = useState<ProductVariant | null>(null);
  const [imageUploadVisible, setImageUploadVisible] = useState(false);

  const [variantForm] = Form.useForm();

  const { data: productData, isLoading: productLoading } = useProduct(productId);
  const { data: variantsData, isLoading: variantsLoading } = useProductVariants(productId);

  const createVariant = useCreateProductVariant();
  const updateVariant = useUpdateProductVariant();
  const deleteVariant = useDeleteProductVariant();
  const uploadImages = useUploadProductImages();

  if (productLoading) {
    return <LoadingSpinner />;
  }

  if (!productData?.data) {
    return (
      <div className="text-center py-8">
        <Text type="secondary">Product not found</Text>
      </div>
    );
  }

  const product = productData.data;

  const handleCreateVariant = async (values: any) => {
    try {
      await createVariant.mutateAsync({ productId, data: values });
      setVariantModalVisible(false);
      variantForm.resetFields();
      setEditingVariant(null);
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleUpdateVariant = async (values: any) => {
    if (!editingVariant) return;
    
    try {
      await updateVariant.mutateAsync({ variantId: editingVariant.id, data: values });
      setVariantModalVisible(false);
      variantForm.resetFields();
      setEditingVariant(null);
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleDeleteVariant = (variantId: string) => {
    Modal.confirm({
      title: 'Delete Variant',
      content: 'Are you sure you want to delete this variant? This action cannot be undone.',
      okText: 'Delete',
      okType: 'danger',
      onOk: () => deleteVariant.mutate(variantId),
    });
  };

  const handleEditVariant = (variant: ProductVariant) => {
    setEditingVariant(variant);
    variantForm.setFieldsValue({
      ...variant,
      attributes: Object.entries(variant.attributes || {}).map(([key, value]) => ({ key, value })),
    });
    setVariantModalVisible(true);
  };

  const handleImageUpload = (info: any) => {
    const { fileList } = info;
    if (fileList.length > 0) {
      const files = fileList.map((file: any) => file.originFileObj).filter(Boolean);
      if (files.length > 0) {
        uploadImages.mutate({ productId, files });
      }
    }
  };

  const getVariantActions = (variant: ProductVariant) => [
    {
      key: 'view',
      label: 'View Details',
      icon: <EyeOutlined />,
      onClick: () => router.push(`/dashboard/products/${productId}/variants/${variant.id}`),
    },
    {
      key: 'edit',
      label: 'Edit Variant',
      icon: <EditOutlined />,
      onClick: () => handleEditVariant(variant),
    },
    {
      key: 'sources',
      label: 'Manage Sources',
      icon: <LinkOutlined />,
      onClick: () => router.push(`/dashboard/products/${productId}/variants/${variant.id}/sources`),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'delete',
      label: 'Delete',
      icon: <DeleteOutlined />,
      danger: true,
      onClick: () => handleDeleteVariant(variant.id),
    },
  ];

  const variantColumns = [
    {
      title: 'Variant',
      key: 'variant',
      render: (record: ProductVariant) => (
        <div className="flex items-center space-x-3">
          {record.mainImageUrl && (
            <Image
              src={record.mainImageUrl}
              alt={record.name}
              width={40}
              height={40}
              className="rounded object-cover"
            />
          )}
          <div>
            <div className="font-medium">{record.name}</div>
            <div className="text-sm text-gray-500">SKU: {record.sku}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Attributes',
      key: 'attributes',
      render: (record: ProductVariant) => (
        <div className="space-y-1">
          {Object.entries(record.attributes || {}).map(([key, value]) => (
            <Tag key={key} className="text-xs">
              {key}: {value}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: 'Price',
      dataIndex: 'myPrice',
      key: 'myPrice',
      render: (price: number, record: ProductVariant) => 
        formatCurrency(price, record.currency),
      sorter: true,
    },
    {
      title: 'Weight',
      key: 'weight',
      render: (record: ProductVariant) => 
        formatWeight(record.weight, record.weightUnit),
    },
    {
      title: 'Stock',
      dataIndex: 'virtualStock',
      key: 'virtualStock',
      render: (stock: number) => (
        <Tag color={stock > 10 ? 'green' : stock > 0 ? 'orange' : 'red'}>
          {stock}
        </Tag>
      ),
      sorter: true,
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 80,
      render: (record: ProductVariant) => (
        <Dropdown
          menu={{ items: getVariantActions(record) }}
          trigger={['click']}
          placement="bottomRight"
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  const tabItems = [
    {
      key: 'overview',
      label: 'Overview',
      children: (
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card title="Product Information">
              <Descriptions column={1} size="small">
                <Descriptions.Item label="Name">{product.name}</Descriptions.Item>
                <Descriptions.Item label="Category">{product.category}</Descriptions.Item>
                <Descriptions.Item label="Status">
                  <StatusBadge status={product.status} type="product" />
                </Descriptions.Item>
                <Descriptions.Item label="Created">
                  {formatDate(product.createdAt)}
                </Descriptions.Item>
                <Descriptions.Item label="Last Updated">
                  {formatDate(product.updatedAt)}
                </Descriptions.Item>
              </Descriptions>
              
              <div className="mt-4">
                <Text strong>Description:</Text>
                <Paragraph className="mt-2 text-gray-600">
                  {product.description}
                </Paragraph>
              </div>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card 
              title="Product Images"
              extra={
                <Button
                  icon={<UploadOutlined />}
                  onClick={() => setImageUploadVisible(true)}
                >
                  Upload Images
                </Button>
              }
            >
              <div className="grid grid-cols-3 gap-2">
                <Image
                  src={product.coverImageUrl}
                  alt="Cover"
                  className="rounded-lg object-cover"
                  height={80}
                />
                {product.images.slice(0, 5).map((image, index) => (
                  <Image
                    key={index}
                    src={image}
                    alt={`Image ${index + 1}`}
                    className="rounded-lg object-cover"
                    height={80}
                  />
                ))}
              </div>
            </Card>
          </Col>
        </Row>
      ),
    },
    {
      key: 'variants',
      label: `Variants (${product.totalVariants})`,
      icon: <ShoppingOutlined />,
      children: (
        <Card
          title="Product Variants"
          extra={
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingVariant(null);
                variantForm.resetFields();
                setVariantModalVisible(true);
              }}
            >
              Add Variant
            </Button>
          }
        >
          {variantsLoading ? (
            <LoadingSpinner />
          ) : (
            <Table
              columns={variantColumns}
              dataSource={variantsData?.data || []}
              rowKey="id"
              pagination={false}
              scroll={{ x: 800 }}
            />
          )}
        </Card>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => router.back()}
          >
            Back
          </Button>
          <div>
            <Title level={3} className="!mb-0">
              {product.name}
            </Title>
            <Text type="secondary">{product.category}</Text>
          </div>
        </div>

        <Space>
          <Button
            icon={<EditOutlined />}
            onClick={() => router.push(`/dashboard/products/${productId}/edit`)}
          >
            Edit Product
          </Button>
        </Space>
      </div>

      {/* Content Tabs */}
      <Tabs items={tabItems} />

      {/* Variant Modal */}
      <Modal
        title={editingVariant ? 'Edit Variant' : 'Add Variant'}
        open={variantModalVisible}
        onCancel={() => {
          setVariantModalVisible(false);
          setEditingVariant(null);
          variantForm.resetFields();
        }}
        onOk={() => variantForm.submit()}
        confirmLoading={createVariant.isPending || updateVariant.isPending}
        width={600}
      >
        <Form
          form={variantForm}
          layout="vertical"
          onFinish={editingVariant ? handleUpdateVariant : handleCreateVariant}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="Variant Name"
                rules={[{ required: true, message: 'Please enter variant name' }]}
              >
                <Input placeholder="e.g., Red / Large" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="sku"
                label="SKU"
                rules={[{ required: true, message: 'Please enter SKU' }]}
              >
                <Input placeholder="e.g., PROD-001-RED-L" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="myPrice"
                label="Price"
                rules={[{ required: true, message: 'Please enter price' }]}
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="0.00"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="currency"
                label="Currency"
                rules={[{ required: true, message: 'Please select currency' }]}
              >
                <Select>
                  {CURRENCIES.map(currency => (
                    <Select.Option key={currency.code} value={currency.code}>
                      {currency.code} - {currency.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="weight"
                label="Weight"
                rules={[{ required: true, message: 'Please enter weight' }]}
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="0.00"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="weightUnit"
                label="Weight Unit"
                rules={[{ required: true, message: 'Please select weight unit' }]}
              >
                <Select>
                  {WEIGHT_UNITS.map(unit => (
                    <Select.Option key={unit.value} value={unit.value}>
                      {unit.label}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="virtualStock"
            label="Virtual Stock"
            rules={[{ required: true, message: 'Please enter stock quantity' }]}
          >
            <InputNumber
              min={0}
              style={{ width: '100%' }}
              placeholder="999"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* Image Upload Modal */}
      <Modal
        title="Upload Product Images"
        open={imageUploadVisible}
        onCancel={() => setImageUploadVisible(false)}
        footer={null}
      >
        <Upload
          multiple
          listType="picture-card"
          accept="image/*"
          onChange={handleImageUpload}
          beforeUpload={() => false} // Prevent auto upload
        >
          <div>
            <PictureOutlined />
            <div style={{ marginTop: 8 }}>Upload</div>
          </div>
        </Upload>
      </Modal>
    </div>
  );
}
