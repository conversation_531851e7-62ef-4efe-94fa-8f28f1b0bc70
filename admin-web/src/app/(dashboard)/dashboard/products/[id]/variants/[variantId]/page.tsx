'use client';

import { useParams, useRouter } from 'next/navigation';
import {
  Card,
  Descriptions,
  Button,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Image,
  Tabs,
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  ShoppingOutlined,
  LinkOutlined,
} from '@ant-design/icons';
import { useProduct, useProductVariants } from '@/lib/hooks/useProducts';
import { SourceLinksManager } from '@/components/forms/SourceLinksManager';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { formatCurrency, formatWeight } from '@/lib/utils/formatters';

const { Title, Text } = Typography;

export default function VariantDetailPage() {
  const params = useParams();
  const router = useRouter();
  const productId = params.id as string;
  const variantId = params.variantId as string;

  const { data: productData, isLoading: productLoading } = useProduct(productId);
  const { data: variantsData, isLoading: variantsLoading } = useProductVariants(productId);

  if (productLoading || variantsLoading) {
    return <LoadingSpinner />;
  }

  if (!productData?.data || !variantsData?.data) {
    return (
      <div className="text-center py-8">
        <Text type="secondary">Product or variant not found</Text>
      </div>
    );
  }

  const product = productData.data;
  const variant = variantsData.data.find(v => v.id === variantId);

  if (!variant) {
    return (
      <div className="text-center py-8">
        <Text type="secondary">Variant not found</Text>
      </div>
    );
  }

  const tabItems = [
    {
      key: 'overview',
      label: 'Overview',
      icon: <ShoppingOutlined />,
      children: (
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card title="Variant Information">
              <Descriptions column={1} size="small">
                <Descriptions.Item label="Name">{variant.name}</Descriptions.Item>
                <Descriptions.Item label="SKU">{variant.sku}</Descriptions.Item>
                <Descriptions.Item label="Price">
                  {formatCurrency(variant.myPrice, variant.currency)}
                </Descriptions.Item>
                <Descriptions.Item label="Weight">
                  {formatWeight(variant.weight, variant.weightUnit)}
                </Descriptions.Item>
                <Descriptions.Item label="Virtual Stock">
                  <Tag color={variant.virtualStock > 10 ? 'green' : variant.virtualStock > 0 ? 'orange' : 'red'}>
                    {variant.virtualStock}
                  </Tag>
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="Attributes">
              <div className="space-y-2">
                {Object.entries(variant.attributes || {}).map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <Text strong>{key}:</Text>
                    <Text>{value}</Text>
                  </div>
                ))}
                {Object.keys(variant.attributes || {}).length === 0 && (
                  <Text type="secondary">No attributes defined</Text>
                )}
              </div>
            </Card>
            
            {variant.mainImageUrl && (
              <Card title="Variant Image" className="mt-4">
                <Image
                  src={variant.mainImageUrl}
                  alt={variant.name}
                  className="rounded-lg"
                  style={{ maxWidth: '100%', maxHeight: 200 }}
                />
              </Card>
            )}
          </Col>
        </Row>
      ),
    },
    {
      key: 'sources',
      label: 'Source Links',
      icon: <LinkOutlined />,
      children: (
        <SourceLinksManager
          variantId={variantId}
          variantName={variant.name}
        />
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => router.back()}
          >
            Back
          </Button>
          <div>
            <Title level={3} className="!mb-0">
              {variant.name}
            </Title>
            <Text type="secondary">
              {product.name} • SKU: {variant.sku}
            </Text>
          </div>
        </div>

        <Space>
          <Button
            icon={<EditOutlined />}
            onClick={() => router.push(`/dashboard/products/${productId}/variants/${variantId}/edit`)}
          >
            Edit Variant
          </Button>
        </Space>
      </div>

      {/* Content Tabs */}
      <Tabs items={tabItems} />
    </div>
  );
}
