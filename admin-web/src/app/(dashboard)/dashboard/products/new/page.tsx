'use client';

import { useRouter } from 'next/navigation';
import { Button, Typography } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { ProductForm } from '@/components/forms/ProductForm';
import { Product } from '@/types';

const { Title } = Typography;

export default function NewProductPage() {
  const router = useRouter();

  const handleSuccess = (product: Product) => {
    router.push(`/dashboard/products/${product.id}`);
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => router.back()}
          >
            Back
          </Button>
          <Title level={3} className="!mb-0">
            Add New Product
          </Title>
        </div>
      </div>

      {/* Product Form */}
      <ProductForm
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
}
