'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Layout, Menu, Avatar, Dropdown, Button, Typography, Space } from 'antd';
import {
  DashboardOutlined,
  UserOutlined,
  ShoppingOutlined,
  ShoppingCartOutlined,
  TeamOutlined,
  BarChartOutlined,
  SettingOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BellOutlined,
} from '@ant-design/icons';
import { useAuthStore } from '@/lib/stores/auth';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

const menuItems = [
  {
    key: '/dashboard',
    icon: <DashboardOutlined />,
    label: 'Dashboard',
  },
  {
    key: '/dashboard/users',
    icon: <UserOutlined />,
    label: 'Users',
  },
  {
    key: '/dashboard/products',
    icon: <ShoppingOutlined />,
    label: 'Products',
  },
  {
    key: '/dashboard/orders',
    icon: <ShoppingCartOutlined />,
    label: 'Orders',
  },
  {
    key: '/dashboard/group-buys',
    icon: <TeamOutlined />,
    label: 'Group Buys',
  },
  {
    key: '/dashboard/analytics',
    icon: <BarChartOutlined />,
    label: 'Analytics',
  },
  {
    key: '/dashboard/analytics',
    icon: <BarChartOutlined />,
    label: 'Analytics',
  },
  {
    key: '/dashboard/content',
    icon: <SettingOutlined />,
    label: 'Content',
  },
];

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, signOut, isInitialized } = useAuthStore();
  const [collapsed, setCollapsed] = useState(false);

  useEffect(() => {
    if (isInitialized && !user) {
      router.replace('/auth/login');
    }
  }, [user, isInitialized, router]);

  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="loading-spinner"></div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect
  }

  const handleMenuClick = ({ key }: { key: string }) => {
    router.push(key);
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/auth/login');
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Profile',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Settings',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Sign Out',
      onClick: handleSignOut,
    },
  ];

  return (
    <Layout className="min-h-screen">
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        className="shadow-lg"
        width={256}
      >
        <div className="p-4">
          <div className="flex items-center justify-center">
            <Text strong className="text-lg text-primary-600">
              {collapsed ? 'M' : 'Maomao Admin'}
            </Text>
          </div>
        </div>
        
        <Menu
          mode="inline"
          selectedKeys={[pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          className="border-r-0"
        />
      </Sider>

      <Layout>
        <Header className="flex items-center justify-between px-6 bg-white shadow-sm">
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            className="text-lg"
          />

          <Space size="middle">
            <Button
              type="text"
              icon={<BellOutlined />}
              className="text-lg"
            />
            
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              trigger={['click']}
            >
              <div className="flex items-center cursor-pointer hover:bg-gray-50 px-3 py-2 rounded-lg">
                <Avatar
                  src={user.photoURL}
                  icon={<UserOutlined />}
                  className="mr-2"
                />
                <div className="hidden sm:block">
                  <Text strong className="block text-sm">
                    {user.displayName || 'Admin User'}
                  </Text>
                  <Text type="secondary" className="text-xs">
                    {user.isSuperAdmin ? 'Super Admin' : 'Admin'}
                  </Text>
                </div>
              </div>
            </Dropdown>
          </Space>
        </Header>

        <Content className="p-6 bg-gray-50">
          {children}
        </Content>
      </Layout>
    </Layout>
  );
}
