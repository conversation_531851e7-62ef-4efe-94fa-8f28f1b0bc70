'use client';

import { useState, useEffect } from 'react';
import {
  Form,
  Input,
  InputNumber,
  DatePicker,
  Select,
  Button,
  Card,
  Row,
  Col,
  Typography,
  Divider,
  Space,
  Alert,
} from 'antd';
import { useProducts, useProductVariants } from '@/lib/hooks/useProducts';
import { useCreateGroupBuy, useUpdateGroupBuy } from '@/lib/hooks/useGroupBuys';
import { CreateGroupBuyData } from '@/lib/api/groupbuys';
import { GroupBuy } from '@/types';
import { formatCurrency } from '@/lib/utils/formatters';
import dayjs from 'dayjs';

const { TextArea } = Input;
const { Title, Text } = Typography;

interface GroupBuyFormProps {
  groupBuy?: GroupBuy;
  onSuccess?: (groupBuy: GroupBuy) => void;
  onCancel?: () => void;
}

export function GroupBuyForm({ groupBuy, onSuccess, onCancel }: GroupBuyFormProps) {
  const [form] = Form.useForm();
  const [selectedProductId, setSelectedProductId] = useState<string>('');
  const [selectedVariant, setSelectedVariant] = useState<any>(null);

  const { data: productsData } = useProducts({ page: 1, limit: 100 });
  const { data: variantsData } = useProductVariants(selectedProductId);
  const createGroupBuy = useCreateGroupBuy();
  const updateGroupBuy = useUpdateGroupBuy();

  const isEditing = !!groupBuy;

  useEffect(() => {
    if (groupBuy) {
      form.setFieldsValue({
        productVariantId: groupBuy.productVariantId,
        targetQuantity: groupBuy.targetQuantity,
        groupPrice: groupBuy.groupPrice,
        expiresAt: dayjs(groupBuy.expiresAt),
      });
      // Set selected product for variant loading
      // This would need to be derived from the groupBuy data
    }
  }, [groupBuy, form]);

  const handleProductChange = (productId: string) => {
    setSelectedProductId(productId);
    form.setFieldsValue({ productVariantId: undefined });
    setSelectedVariant(null);
  };

  const handleVariantChange = (variantId: string) => {
    const variant = variantsData?.data.find(v => v.id === variantId);
    setSelectedVariant(variant);
  };

  const calculateSavings = () => {
    if (!selectedVariant) return 0;
    const groupPrice = form.getFieldValue('groupPrice') || 0;
    const targetQuantity = form.getFieldValue('targetQuantity') || 0;
    const regularPrice = selectedVariant.myPrice;
    return (regularPrice - groupPrice) * targetQuantity;
  };

  const calculateDiscountPercentage = () => {
    if (!selectedVariant) return 0;
    const groupPrice = form.getFieldValue('groupPrice') || 0;
    const regularPrice = selectedVariant.myPrice;
    return ((regularPrice - groupPrice) / regularPrice) * 100;
  };

  const handleSubmit = async (values: any) => {
    try {
      const groupBuyData: CreateGroupBuyData = {
        productVariantId: values.productVariantId,
        targetQuantity: values.targetQuantity,
        groupPrice: values.groupPrice,
        expiresAt: values.expiresAt.toDate(),
        description: values.description,
        minParticipants: values.minParticipants,
        maxParticipants: values.maxParticipants,
      };

      let result;
      if (isEditing && groupBuy) {
        result = await updateGroupBuy.mutateAsync({
          groupBuyId: groupBuy.id,
          data: groupBuyData,
        });
      } else {
        result = await createGroupBuy.mutateAsync(groupBuyData);
      }

      if (onSuccess) {
        onSuccess(result.data);
      }
    } catch (error) {
      // Error handled by hooks
    }
  };

  const savings = calculateSavings();
  const discountPercentage = calculateDiscountPercentage();

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      className="space-y-6"
    >
      <Card title="Product Selection">
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="productId"
              label="Product"
              rules={[{ required: true, message: 'Please select a product' }]}
            >
              <Select
                placeholder="Select product"
                showSearch
                optionFilterProp="children"
                onChange={handleProductChange}
              >
                {productsData?.data.items.map(product => (
                  <Select.Option key={product.id} value={product.id}>
                    {product.name}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="productVariantId"
              label="Product Variant"
              rules={[{ required: true, message: 'Please select a variant' }]}
            >
              <Select
                placeholder="Select variant"
                disabled={!selectedProductId}
                onChange={handleVariantChange}
              >
                {variantsData?.data.map(variant => (
                  <Select.Option key={variant.id} value={variant.id}>
                    {variant.name} - {formatCurrency(variant.myPrice, variant.currency)}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        {selectedVariant && (
          <Alert
            message="Selected Variant"
            description={
              <div>
                <Text strong>{selectedVariant.name}</Text>
                <br />
                <Text>SKU: {selectedVariant.sku}</Text>
                <br />
                <Text>Regular Price: {formatCurrency(selectedVariant.myPrice, selectedVariant.currency)}</Text>
                <br />
                <Text>Available Stock: {selectedVariant.virtualStock}</Text>
              </div>
            }
            type="info"
            className="mt-4"
          />
        )}
      </Card>

      <Card title="Group Buy Configuration">
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="targetQuantity"
              label="Target Quantity"
              rules={[
                { required: true, message: 'Please enter target quantity' },
                { type: 'number', min: 2, message: 'Target quantity must be at least 2' },
              ]}
            >
              <InputNumber
                min={2}
                style={{ width: '100%' }}
                placeholder="Minimum 2"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="groupPrice"
              label="Group Buy Price"
              rules={[
                { required: true, message: 'Please enter group buy price' },
                { type: 'number', min: 0.01, message: 'Price must be greater than 0' },
              ]}
            >
              <InputNumber
                min={0.01}
                precision={2}
                style={{ width: '100%' }}
                placeholder="0.00"
                addonBefore={selectedVariant?.currency || '$'}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="minParticipants"
              label="Minimum Participants (Optional)"
            >
              <InputNumber
                min={1}
                style={{ width: '100%' }}
                placeholder="Optional"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="maxParticipants"
              label="Maximum Participants (Optional)"
            >
              <InputNumber
                min={1}
                style={{ width: '100%' }}
                placeholder="Optional"
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="expiresAt"
          label="Expiration Date & Time"
          rules={[{ required: true, message: 'Please select expiration date' }]}
        >
          <DatePicker
            showTime
            style={{ width: '100%' }}
            disabledDate={(current) => current && current < dayjs().endOf('day')}
            placeholder="Select expiration date and time"
          />
        </Form.Item>

        <Form.Item
          name="description"
          label="Description (Optional)"
        >
          <TextArea
            rows={4}
            placeholder="Optional description for the group buy..."
            maxLength={1000}
            showCount
          />
        </Form.Item>
      </Card>

      {selectedVariant && (
        <Card title="Savings Calculation">
          <Row gutter={16}>
            <Col span={8}>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {discountPercentage.toFixed(1)}%
                </div>
                <div className="text-sm text-gray-500">Discount</div>
              </div>
            </Col>
            <Col span={8}>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {formatCurrency(savings, selectedVariant.currency)}
                </div>
                <div className="text-sm text-gray-500">Total Savings</div>
              </div>
            </Col>
            <Col span={8}>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {formatCurrency(
                    (form.getFieldValue('groupPrice') || 0) * (form.getFieldValue('targetQuantity') || 0),
                    selectedVariant.currency
                  )}
                </div>
                <div className="text-sm text-gray-500">Total Value</div>
              </div>
            </Col>
          </Row>
        </Card>
      )}

      <Divider />

      <div className="flex justify-end space-x-4">
        {onCancel && (
          <Button onClick={onCancel}>
            Cancel
          </Button>
        )}
        <Button
          type="primary"
          htmlType="submit"
          loading={createGroupBuy.isPending || updateGroupBuy.isPending}
          disabled={!selectedVariant}
        >
          {isEditing ? 'Update Group Buy' : 'Create Group Buy'}
        </Button>
      </div>
    </Form>
  );
}
