'use client';

import { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  Upload,
  Button,
  Card,
  Row,
  Col,
  message,
  Image,
} from 'antd';
import {
  UploadOutlined,
  PlusOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { useCategories, useCreateProduct, useUpdateProduct } from '@/lib/hooks/useProducts';
import { Product } from '@/types';
import { PRODUCT_STATUSES } from '@/constants';
import { validateImageFile } from '@/lib/utils/validation';

const { TextArea } = Input;
const { Option } = Select;

interface ProductFormProps {
  product?: Product;
  onSuccess?: (product: Product) => void;
  onCancel?: () => void;
}

export function ProductForm({ product, onSuccess, onCancel }: ProductFormProps) {
  const [form] = Form.useForm();
  const [coverImageFile, setCoverImageFile] = useState<File | null>(null);
  const [additionalImageFiles, setAdditionalImageFiles] = useState<File[]>([]);
  const [coverImagePreview, setCoverImagePreview] = useState<string>('');
  const [additionalImagePreviews, setAdditionalImagePreviews] = useState<string[]>([]);

  const { data: categoriesData } = useCategories();
  const createProduct = useCreateProduct();
  const updateProduct = useUpdateProduct();

  const isEditing = !!product;

  useEffect(() => {
    if (product) {
      form.setFieldsValue({
        name: product.name,
        description: product.description,
        category: product.category,
        status: product.status,
      });
      setCoverImagePreview(product.coverImageUrl);
      setAdditionalImagePreviews(product.images || []);
    }
  }, [product, form]);

  const handleCoverImageChange = (info: any) => {
    const file = info.file.originFileObj || info.file;
    if (!file) return;

    const error = validateImageFile(file);
    if (error) {
      message.error(error);
      return;
    }

    setCoverImageFile(file);
    
    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setCoverImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  const handleAdditionalImagesChange = (info: any) => {
    const files = info.fileList.map((file: any) => file.originFileObj || file).filter(Boolean);
    
    // Validate each file
    for (const file of files) {
      const error = validateImageFile(file);
      if (error) {
        message.error(`${file.name}: ${error}`);
        return;
      }
    }

    setAdditionalImageFiles(files);
    
    // Create previews
    const previews: string[] = [];
    let loadedCount = 0;
    
    files.forEach((file: File) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        previews.push(e.target?.result as string);
        loadedCount++;
        if (loadedCount === files.length) {
          setAdditionalImagePreviews(previews);
        }
      };
      reader.readAsDataURL(file);
    });
  };

  const handleSubmit = async (values: any) => {
    try {
      const productData = {
        name: values.name,
        description: values.description,
        category: values.category,
        status: values.status,
        coverImageUrl: coverImagePreview, // Will be updated after image upload
        images: additionalImagePreviews, // Will be updated after image upload
      };

      let result;
      if (isEditing && product) {
        result = await updateProduct.mutateAsync({
          productId: product.id,
          data: productData,
        });
      } else {
        result = await createProduct.mutateAsync(productData);
      }

      // TODO: Handle image uploads to Firebase Storage
      // This would typically involve uploading the files and updating the product with the URLs

      if (onSuccess) {
        onSuccess(result.data);
      }
    } catch (error) {
      // Error handled by hooks
    }
  };

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      className="space-y-6"
    >
      <Card title="Basic Information">
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="name"
              label="Product Name"
              rules={[
                { required: true, message: 'Please enter product name' },
                { max: 255, message: 'Name must be less than 255 characters' },
              ]}
            >
              <Input placeholder="Enter product name" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="category"
              label="Category"
              rules={[{ required: true, message: 'Please select category' }]}
            >
              <Select placeholder="Select category" showSearch>
                {categoriesData?.data.map(category => (
                  <Option key={category.id} value={category.name}>
                    {category.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="status"
              label="Status"
              rules={[{ required: true, message: 'Please select status' }]}
            >
              <Select placeholder="Select status">
                {PRODUCT_STATUSES.map(status => (
                  <Option key={status} value={status}>
                    {status}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="description"
          label="Description"
          rules={[
            { required: true, message: 'Please enter product description' },
            { max: 5000, message: 'Description must be less than 5000 characters' },
          ]}
        >
          <TextArea
            rows={6}
            placeholder="Enter detailed product description..."
            showCount
            maxLength={5000}
          />
        </Form.Item>
      </Card>

      <Card title="Product Images">
        <Row gutter={16}>
          <Col span={12}>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Cover Image *
              </label>
              <Upload
                listType="picture-card"
                showUploadList={false}
                beforeUpload={() => false}
                onChange={handleCoverImageChange}
                accept="image/*"
              >
                {coverImagePreview ? (
                  <Image
                    src={coverImagePreview}
                    alt="Cover"
                    style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                    preview={false}
                  />
                ) : (
                  uploadButton
                )}
              </Upload>
              <p className="text-xs text-gray-500">
                Recommended size: 800x800px. Max file size: 5MB.
              </p>
            </div>
          </Col>
          <Col span={12}>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Additional Images
              </label>
              <Upload
                listType="picture-card"
                multiple
                beforeUpload={() => false}
                onChange={handleAdditionalImagesChange}
                accept="image/*"
                fileList={additionalImageFiles.map((file, index) => ({
                  uid: index.toString(),
                  name: file.name,
                  status: 'done' as const,
                  url: additionalImagePreviews[index],
                }))}
              >
                {additionalImageFiles.length >= 8 ? null : uploadButton}
              </Upload>
              <p className="text-xs text-gray-500">
                Upload up to 8 additional images. Max file size: 5MB each.
              </p>
            </div>
          </Col>
        </Row>
      </Card>

      <div className="flex justify-end space-x-4">
        {onCancel && (
          <Button onClick={onCancel}>
            Cancel
          </Button>
        )}
        <Button
          type="primary"
          htmlType="submit"
          loading={createProduct.isPending || updateProduct.isPending}
        >
          {isEditing ? 'Update Product' : 'Create Product'}
        </Button>
      </div>
    </Form>
  );
}
