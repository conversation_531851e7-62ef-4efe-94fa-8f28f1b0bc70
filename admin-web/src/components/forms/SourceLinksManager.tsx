'use client';

import { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Space,
  Tag,
  Tooltip,
  message,
  Typography,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  LinkOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import {
  useVariantSourceLinks,
  useCreateSourceLink,
  useUpdateSourceLink,
  useDeleteSourceLink,
} from '@/lib/hooks/useProducts';
import { SourceLink } from '@/types';
import { SOURCE_PLATFORMS, CURRENCIES } from '@/constants';
import { formatDate, formatCurrency } from '@/lib/utils/formatters';

const { Text } = Typography;
const { TextArea } = Input;

interface SourceLinksManagerProps {
  variantId: string;
  variantName: string;
}

export function SourceLinksManager({ variantId, variantName }: SourceLinksManagerProps) {
  const [modalVisible, setModalVisible] = useState(false);
  const [editingSource, setEditingSource] = useState<SourceLink | null>(null);
  const [form] = Form.useForm();

  const { data: sourceLinksData, isLoading } = useVariantSourceLinks(variantId);
  const createSourceLink = useCreateSourceLink();
  const updateSourceLink = useUpdateSourceLink();
  const deleteSourceLink = useDeleteSourceLink();

  const handleCreate = async (values: any) => {
    try {
      await createSourceLink.mutateAsync({ variantId, data: values });
      setModalVisible(false);
      form.resetFields();
      setEditingSource(null);
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleUpdate = async (values: any) => {
    if (!editingSource) return;
    
    try {
      await updateSourceLink.mutateAsync({
        sourceLinkId: editingSource.id,
        data: values,
      });
      setModalVisible(false);
      form.resetFields();
      setEditingSource(null);
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleEdit = (sourceLink: SourceLink) => {
    setEditingSource(sourceLink);
    form.setFieldsValue(sourceLink);
    setModalVisible(true);
  };

  const handleDelete = (sourceLinkId: string) => {
    Modal.confirm({
      title: 'Delete Source Link',
      icon: <ExclamationCircleOutlined />,
      content: 'Are you sure you want to delete this source link? This action cannot be undone.',
      okText: 'Delete',
      okType: 'danger',
      onOk: () => deleteSourceLink.mutate(sourceLinkId),
    });
  };

  const handleOpenUrl = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const columns = [
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority: number) => (
        <Tag color={priority === 1 ? 'gold' : priority === 2 ? 'silver' : 'default'}>
          {priority}
        </Tag>
      ),
      sorter: (a: SourceLink, b: SourceLink) => a.priority - b.priority,
    },
    {
      title: 'Platform',
      dataIndex: 'sourcePlatform',
      key: 'sourcePlatform',
      render: (platform: string) => (
        <Tag color="blue">{platform}</Tag>
      ),
      filters: SOURCE_PLATFORMS.map(platform => ({ text: platform, value: platform })),
      onFilter: (value: any, record: SourceLink) => record.sourcePlatform === value,
    },
    {
      title: 'Source URL',
      dataIndex: 'sourceUrl',
      key: 'sourceUrl',
      render: (url: string) => (
        <div className="flex items-center space-x-2">
          <Text
            className="max-w-xs truncate cursor-pointer text-blue-600 hover:text-blue-800"
            onClick={() => handleOpenUrl(url)}
          >
            {url}
          </Text>
          <Button
            type="text"
            size="small"
            icon={<LinkOutlined />}
            onClick={() => handleOpenUrl(url)}
          />
        </div>
      ),
    },
    {
      title: 'Source Price',
      key: 'sourcePrice',
      render: (record: SourceLink) => 
        formatCurrency(record.sourcePrice, record.sourceCurrency),
      sorter: (a: SourceLink, b: SourceLink) => a.sourcePrice - b.sourcePrice,
    },
    {
      title: 'Last Checked',
      dataIndex: 'lastCheckedAt',
      key: 'lastCheckedAt',
      render: (date: Date) => (
        <Tooltip title={formatDate(date)}>
          {formatDate(date, 'MMM DD')}
        </Tooltip>
      ),
      sorter: (a: SourceLink, b: SourceLink) => 
        new Date(a.lastCheckedAt).getTime() - new Date(b.lastCheckedAt).getTime(),
    },
    {
      title: 'Notes',
      dataIndex: 'notes',
      key: 'notes',
      render: (notes: string) => (
        <Tooltip title={notes}>
          <Text className="max-w-xs truncate">
            {notes || '-'}
          </Text>
        </Tooltip>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (record: SourceLink) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          />
          <Button
            type="text"
            size="small"
            icon={<DeleteOutlined />}
            danger
            onClick={() => handleDelete(record.id)}
          />
        </Space>
      ),
    },
  ];

  return (
    <Card
      title={`Source Links for ${variantName}`}
      extra={
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setEditingSource(null);
            form.resetFields();
            setModalVisible(true);
          }}
        >
          Add Source Link
        </Button>
      }
    >
      <Table
        columns={columns}
        dataSource={sourceLinksData?.data || []}
        rowKey="id"
        loading={isLoading}
        pagination={false}
        scroll={{ x: 800 }}
        locale={{ emptyText: 'No source links found' }}
      />

      <Modal
        title={editingSource ? 'Edit Source Link' : 'Add Source Link'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingSource(null);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        confirmLoading={createSourceLink.isPending || updateSourceLink.isPending}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={editingSource ? handleUpdate : handleCreate}
        >
          <Form.Item
            name="sourcePlatform"
            label="Source Platform"
            rules={[{ required: true, message: 'Please select source platform' }]}
          >
            <Select placeholder="Select platform">
              {SOURCE_PLATFORMS.map(platform => (
                <Select.Option key={platform} value={platform}>
                  {platform}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="sourceUrl"
            label="Source URL"
            rules={[
              { required: true, message: 'Please enter source URL' },
              { type: 'url', message: 'Please enter a valid URL' },
            ]}
          >
            <Input placeholder="https://example.com/product" />
          </Form.Item>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              name="sourcePrice"
              label="Source Price"
              rules={[{ required: true, message: 'Please enter source price' }]}
            >
              <InputNumber
                min={0}
                precision={2}
                style={{ width: '100%' }}
                placeholder="0.00"
              />
            </Form.Item>

            <Form.Item
              name="sourceCurrency"
              label="Currency"
              rules={[{ required: true, message: 'Please select currency' }]}
            >
              <Select placeholder="Select currency">
                {CURRENCIES.map(currency => (
                  <Select.Option key={currency.code} value={currency.code}>
                    {currency.code} - {currency.name}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </div>

          <Form.Item
            name="priority"
            label="Priority"
            rules={[{ required: true, message: 'Please enter priority' }]}
            tooltip="Lower numbers have higher priority (1 = highest priority)"
          >
            <InputNumber
              min={1}
              max={10}
              style={{ width: '100%' }}
              placeholder="1"
            />
          </Form.Item>

          <Form.Item
            name="notes"
            label="Notes"
          >
            <TextArea
              rows={3}
              placeholder="Optional notes about this source..."
              maxLength={500}
              showCount
            />
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
}
