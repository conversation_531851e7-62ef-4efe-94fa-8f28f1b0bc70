'use client';

import { useEffect } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { useAuthStore } from '@/lib/stores/auth';
import { 
  initPostHog, 
  identifyAdmin, 
  trackPageView, 
  setAdminProperties, 
  resetAdmin,
  POSTHOG_PROPERTIES 
} from '@/lib/posthog/config';

interface PostHogProviderProps {
  children: React.ReactNode;
}

export function PostHogProvider({ children }: PostHogProviderProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { user, isInitialized } = useAuthStore();

  // Initialize PostHog
  useEffect(() => {
    initPostHog();
  }, []);

  // Track page views
  useEffect(() => {
    if (pathname) {
      const url = `${pathname}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
      
      // Get page title from pathname
      const getPageTitle = (path: string) => {
        const segments = path.split('/').filter(Boolean);
        if (segments.length === 0) return 'Home';
        if (segments[0] === 'dashboard') {
          if (segments.length === 1) return 'Dashboard';
          return segments.slice(1).map(s => 
            s.split('-').map(word => 
              word.charAt(0).toUpperCase() + word.slice(1)
            ).join(' ')
          ).join(' - ');
        }
        return segments.join(' - ');
      };

      trackPageView(getPageTitle(pathname), {
        [POSTHOG_PROPERTIES.PAGE_URL]: url,
        pathname,
        search_params: searchParams.toString(),
      });
    }
  }, [pathname, searchParams]);

  // Identify admin user
  useEffect(() => {
    if (isInitialized) {
      if (user) {
        identifyAdmin(user.id, {
          [POSTHOG_PROPERTIES.ADMIN_EMAIL]: user.email,
          [POSTHOG_PROPERTIES.ADMIN_ROLE]: user.isSuperAdmin ? 'super_admin' : 'admin',
          display_name: user.displayName,
          is_super_admin: user.isSuperAdmin,
          created_at: user.createdAt,
        });

        setAdminProperties({
          email: user.email,
          display_name: user.displayName,
          is_admin: true,
          is_super_admin: user.isSuperAdmin,
          last_login: new Date().toISOString(),
        });
      } else {
        resetAdmin();
      }
    }
  }, [user, isInitialized]);

  return <>{children}</>;
}
