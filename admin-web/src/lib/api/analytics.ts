import { apiClient } from './client';
import { ApiResponse } from '@/types';

export interface AnalyticsOverview {
  totalRevenue: number;
  totalOrders: number;
  totalUsers: number;
  totalProducts: number;
  revenueGrowth: number;
  orderGrowth: number;
  userGrowth: number;
  conversionRate: number;
  averageOrderValue: number;
  customerLifetimeValue: number;
  topSellingProducts: TopProduct[];
  recentActivity: ActivityItem[];
}

export interface TopProduct {
  id: string;
  name: string;
  category: string;
  totalSold: number;
  revenue: number;
  imageUrl?: string;
}

export interface ActivityItem {
  id: string;
  type: 'order' | 'user' | 'product' | 'group_buy';
  description: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface RevenueAnalytics {
  totalRevenue: number;
  revenueByPeriod: Array<{
    period: string;
    revenue: number;
    orders: number;
    averageOrderValue: number;
  }>;
  revenueByCategory: Array<{
    category: string;
    revenue: number;
    percentage: number;
  }>;
  revenueBySource: Array<{
    source: 'regular' | 'group_buy';
    revenue: number;
    percentage: number;
  }>;
  topCustomers: Array<{
    userId: string;
    email: string;
    displayName: string;
    totalSpent: number;
    orderCount: number;
  }>;
}

export interface UserAnalytics {
  totalUsers: number;
  activeUsers: number;
  newUsers: number;
  userGrowth: Array<{
    period: string;
    newUsers: number;
    totalUsers: number;
    retentionRate: number;
  }>;
  userDemographics: {
    byCountry: Array<{ country: string; count: number; percentage: number }>;
    byRegistrationSource: Array<{ source: string; count: number; percentage: number }>;
  };
  userBehavior: {
    averageSessionDuration: number;
    averageOrdersPerUser: number;
    repeatPurchaseRate: number;
    churnRate: number;
  };
}

export interface ProductAnalytics {
  totalProducts: number;
  activeProducts: number;
  productPerformance: Array<{
    productId: string;
    name: string;
    category: string;
    views: number;
    orders: number;
    revenue: number;
    conversionRate: number;
    stockLevel: number;
  }>;
  categoryPerformance: Array<{
    category: string;
    productCount: number;
    totalSales: number;
    revenue: number;
    averagePrice: number;
  }>;
  inventoryAnalytics: {
    lowStockProducts: number;
    outOfStockProducts: number;
    totalInventoryValue: number;
    turnoverRate: number;
  };
}

export interface GroupBuyAnalytics {
  totalGroupBuys: number;
  activeGroupBuys: number;
  successfulGroupBuys: number;
  successRate: number;
  totalParticipants: number;
  totalSavings: number;
  groupBuyPerformance: Array<{
    groupBuyId: string;
    productName: string;
    targetQuantity: number;
    currentQuantity: number;
    participantCount: number;
    progressPercentage: number;
    totalValue: number;
    status: string;
    createdAt: Date;
    expiresAt: Date;
  }>;
  participationTrends: Array<{
    period: string;
    newParticipants: number;
    totalParticipants: number;
    averageParticipationPerGroupBuy: number;
  }>;
}

export interface ConversionAnalytics {
  overallConversionRate: number;
  conversionFunnel: Array<{
    stage: string;
    users: number;
    conversionRate: number;
    dropOffRate: number;
  }>;
  conversionBySource: Array<{
    source: string;
    visitors: number;
    conversions: number;
    conversionRate: number;
  }>;
  conversionByDevice: Array<{
    device: string;
    visitors: number;
    conversions: number;
    conversionRate: number;
  }>;
}

export interface PerformanceMetrics {
  pageLoadTimes: Array<{
    page: string;
    averageLoadTime: number;
    p95LoadTime: number;
    bounceRate: number;
  }>;
  apiPerformance: Array<{
    endpoint: string;
    averageResponseTime: number;
    errorRate: number;
    requestCount: number;
  }>;
  errorAnalytics: {
    totalErrors: number;
    errorsByType: Array<{ type: string; count: number; percentage: number }>;
    errorTrends: Array<{ period: string; errorCount: number; errorRate: number }>;
  };
}

export interface AnalyticsFilters {
  dateFrom?: Date;
  dateTo?: Date;
  period?: 'hour' | 'day' | 'week' | 'month' | 'quarter' | 'year';
  category?: string;
  source?: string;
  country?: string;
  device?: string;
}

// Analytics API functions
export const analyticsApi = {
  // Get overview analytics
  async getOverview(filters?: AnalyticsFilters): Promise<ApiResponse<AnalyticsOverview>> {
    return apiClient.get('/api/admin/analytics/overview', filters);
  },

  // Get revenue analytics
  async getRevenueAnalytics(filters?: AnalyticsFilters): Promise<ApiResponse<RevenueAnalytics>> {
    return apiClient.get('/api/admin/analytics/revenue', filters);
  },

  // Get user analytics
  async getUserAnalytics(filters?: AnalyticsFilters): Promise<ApiResponse<UserAnalytics>> {
    return apiClient.get('/api/admin/analytics/users', filters);
  },

  // Get product analytics
  async getProductAnalytics(filters?: AnalyticsFilters): Promise<ApiResponse<ProductAnalytics>> {
    return apiClient.get('/api/admin/analytics/products', filters);
  },

  // Get group buy analytics
  async getGroupBuyAnalytics(filters?: AnalyticsFilters): Promise<ApiResponse<GroupBuyAnalytics>> {
    return apiClient.get('/api/admin/analytics/group-buys', filters);
  },

  // Get conversion analytics
  async getConversionAnalytics(filters?: AnalyticsFilters): Promise<ApiResponse<ConversionAnalytics>> {
    return apiClient.get('/api/admin/analytics/conversion', filters);
  },

  // Get performance metrics
  async getPerformanceMetrics(filters?: AnalyticsFilters): Promise<ApiResponse<PerformanceMetrics>> {
    return apiClient.get('/api/admin/analytics/performance', filters);
  },

  // Get PostHog insights
  async getPostHogInsights(query: {
    insight: 'trends' | 'funnels' | 'retention' | 'paths' | 'lifecycle';
    events?: string[];
    properties?: Record<string, any>;
    dateFrom?: string;
    dateTo?: string;
    interval?: 'hour' | 'day' | 'week' | 'month';
  }): Promise<ApiResponse<any>> {
    return apiClient.post('/api/admin/analytics/posthog/insights', query);
  },

  // Get custom dashboard data
  async getDashboardData(dashboardId: string, filters?: AnalyticsFilters): Promise<ApiResponse<any>> {
    return apiClient.get(`/api/admin/analytics/dashboards/${dashboardId}`, filters);
  },

  // Export analytics report
  async exportReport(type: 'overview' | 'revenue' | 'users' | 'products' | 'group-buys', filters?: AnalyticsFilters): Promise<ApiResponse<{ downloadUrl: string }>> {
    return apiClient.post('/api/admin/analytics/export', { type, filters });
  },

  // Get real-time metrics
  async getRealTimeMetrics(): Promise<ApiResponse<{
    activeUsers: number;
    currentOrders: number;
    revenueToday: number;
    activeGroupBuys: number;
    systemHealth: {
      apiResponseTime: number;
      errorRate: number;
      uptime: number;
    };
  }>> {
    return apiClient.get('/api/admin/analytics/realtime');
  },

  // Get cohort analysis
  async getCohortAnalysis(filters?: AnalyticsFilters): Promise<ApiResponse<{
    cohorts: Array<{
      cohortPeriod: string;
      userCount: number;
      retentionRates: number[];
    }>;
    averageRetention: number[];
  }>> {
    return apiClient.get('/api/admin/analytics/cohorts', filters);
  },

  // Get A/B test results
  async getABTestResults(testId?: string): Promise<ApiResponse<Array<{
    testId: string;
    testName: string;
    status: 'running' | 'completed' | 'paused';
    variants: Array<{
      name: string;
      participants: number;
      conversionRate: number;
      significance: number;
    }>;
    startDate: Date;
    endDate?: Date;
  }>>> {
    return apiClient.get('/api/admin/analytics/ab-tests', testId ? { testId } : undefined);
  },
};
