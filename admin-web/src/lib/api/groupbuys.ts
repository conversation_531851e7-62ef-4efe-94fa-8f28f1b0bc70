import { apiClient } from './client';
import { GroupBuy, ApiResponse, PaginatedResponse } from '@/types';

export interface GroupBuyWithDetails extends GroupBuy {
  productVariant: {
    id: string;
    name: string;
    sku: string;
    mainImageUrl?: string;
    myPrice: number;
    currency: string;
    product: {
      id: string;
      name: string;
      category: string;
    };
  };
  participants: GroupBuyParticipant[];
  participantCount: number;
  progressPercentage: number;
  timeRemaining: number; // in milliseconds
  canFulfill: boolean;
  estimatedSavings: number;
  totalValue: number;
}

export interface GroupBuyParticipant {
  id: string;
  userId: string;
  groupBuyId: string;
  quantity: number;
  joinedAt: Date;
  user: {
    id: string;
    email: string;
    displayName: string;
  };
}

export interface GroupBuyStats {
  totalGroupBuys: number;
  activeGroupBuys: number;
  successfulGroupBuys: number;
  failedGroupBuys: number;
  fulfilledGroupBuys: number;
  totalParticipants: number;
  totalSavings: number;
  averageParticipants: number;
  successRate: number;
}

export interface GroupBuyFilters {
  status?: string;
  productVariantId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  search?: string;
  page?: number;
  limit?: number;
}

export interface CreateGroupBuyData {
  productVariantId: string;
  targetQuantity: number;
  groupPrice: number;
  expiresAt: Date;
  description?: string;
  minParticipants?: number;
  maxParticipants?: number;
}

// Group Buy management API functions
export const groupBuyApi = {
  // Get paginated list of group buys with filters
  async getGroupBuys(filters: GroupBuyFilters): Promise<ApiResponse<PaginatedResponse<GroupBuyWithDetails>>> {
    return apiClient.get('/api/admin/groupbuys', filters);
  },

  // Get detailed group buy information
  async getGroupBuyById(groupBuyId: string): Promise<ApiResponse<GroupBuyWithDetails>> {
    return apiClient.get(`/api/admin/groupbuys/${groupBuyId}`);
  },

  // Create new group buy
  async createGroupBuy(data: CreateGroupBuyData): Promise<ApiResponse<GroupBuy>> {
    return apiClient.post('/api/admin/groupbuys', data);
  },

  // Update group buy
  async updateGroupBuy(groupBuyId: string, data: Partial<GroupBuy>): Promise<ApiResponse<GroupBuy>> {
    return apiClient.put(`/api/admin/groupbuys/${groupBuyId}`, data);
  },

  // Update group buy status
  async updateGroupBuyStatus(groupBuyId: string, status: string, notes?: string): Promise<ApiResponse<GroupBuy>> {
    return apiClient.put(`/api/admin/groupbuys/${groupBuyId}/status`, { status, notes });
  },

  // Delete group buy (soft delete)
  async deleteGroupBuy(groupBuyId: string): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/admin/groupbuys/${groupBuyId}`);
  },

  // Get group buy participants
  async getGroupBuyParticipants(groupBuyId: string): Promise<ApiResponse<GroupBuyParticipant[]>> {
    return apiClient.get(`/api/admin/groupbuys/${groupBuyId}/participants`);
  },

  // Remove participant from group buy
  async removeParticipant(groupBuyId: string, participantId: string, reason?: string): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/admin/groupbuys/${groupBuyId}/participants/${participantId}`, { reason });
  },

  // Manually add participant to group buy
  async addParticipant(groupBuyId: string, data: {
    userId: string;
    quantity: number;
    notes?: string;
  }): Promise<ApiResponse<GroupBuyParticipant>> {
    return apiClient.post(`/api/admin/groupbuys/${groupBuyId}/participants`, data);
  },

  // Extend group buy deadline
  async extendDeadline(groupBuyId: string, newExpiresAt: Date, reason?: string): Promise<ApiResponse<GroupBuy>> {
    return apiClient.put(`/api/admin/groupbuys/${groupBuyId}/extend`, { expiresAt: newExpiresAt, reason });
  },

  // Force complete group buy (even if target not reached)
  async forceComplete(groupBuyId: string, reason: string): Promise<ApiResponse<GroupBuy>> {
    return apiClient.put(`/api/admin/groupbuys/${groupBuyId}/force-complete`, { reason });
  },

  // Mark group buy as fulfilled
  async markFulfilled(groupBuyId: string, data: {
    fulfillmentNotes?: string;
    trackingNumbers?: string[];
    estimatedDelivery?: Date;
  }): Promise<ApiResponse<GroupBuy>> {
    return apiClient.put(`/api/admin/groupbuys/${groupBuyId}/fulfill`, data);
  },

  // Get group buy statistics
  async getGroupBuyStats(dateRange?: { from: Date; to: Date }): Promise<ApiResponse<GroupBuyStats>> {
    return apiClient.get('/api/admin/groupbuys/stats', dateRange);
  },

  // Get active group buys requiring attention
  async getActionRequiredGroupBuys(): Promise<ApiResponse<GroupBuyWithDetails[]>> {
    return apiClient.get('/api/admin/groupbuys/action-required');
  },

  // Get expiring group buys
  async getExpiringGroupBuys(hours: number = 24): Promise<ApiResponse<GroupBuyWithDetails[]>> {
    return apiClient.get('/api/admin/groupbuys/expiring', { hours });
  },

  // Search group buys
  async searchGroupBuys(query: string, limit: number = 10): Promise<ApiResponse<GroupBuyWithDetails[]>> {
    return apiClient.get('/api/admin/groupbuys/search', { query, limit });
  },

  // Export group buys
  async exportGroupBuys(filters: GroupBuyFilters): Promise<ApiResponse<{ downloadUrl: string }>> {
    return apiClient.post('/api/admin/groupbuys/export', filters);
  },

  // Get group buy analytics
  async getGroupBuyAnalytics(period: 'day' | 'week' | 'month' | 'year'): Promise<ApiResponse<{
    groupBuysByStatus: Record<string, number>;
    participationByDay: Array<{ date: string; participants: number; groupBuys: number }>;
    topProducts: Array<{ productVariantId: string; productName: string; groupBuyCount: number; totalParticipants: number }>;
    successRateByCategory: Record<string, number>;
    averageTimeToComplete: number;
  }>> {
    return apiClient.get('/api/admin/groupbuys/analytics', { period });
  },

  // Duplicate group buy
  async duplicateGroupBuy(groupBuyId: string, data: {
    targetQuantity?: number;
    groupPrice?: number;
    expiresAt: Date;
  }): Promise<ApiResponse<GroupBuy>> {
    return apiClient.post(`/api/admin/groupbuys/${groupBuyId}/duplicate`, data);
  },

  // Send notification to participants
  async notifyParticipants(groupBuyId: string, data: {
    title: string;
    message: string;
    type: 'info' | 'warning' | 'success' | 'error';
    sendEmail?: boolean;
    sendPush?: boolean;
  }): Promise<ApiResponse<void>> {
    return apiClient.post(`/api/admin/groupbuys/${groupBuyId}/notify`, data);
  },

  // Get group buy performance metrics
  async getGroupBuyMetrics(groupBuyId: string): Promise<ApiResponse<{
    dailyParticipation: Array<{ date: string; newParticipants: number; totalQuantity: number }>;
    participantDemographics: {
      newUsers: number;
      returningUsers: number;
      averageOrderValue: number;
    };
    conversionRate: number;
    shareRate: number;
    completionProbability: number;
  }>> {
    return apiClient.get(`/api/admin/groupbuys/${groupBuyId}/metrics`);
  },

  // Bulk update group buys
  async bulkUpdateGroupBuys(groupBuyIds: string[], data: {
    status?: string;
    notes?: string;
  }): Promise<ApiResponse<void>> {
    return apiClient.put('/api/admin/groupbuys/bulk', { groupBuyIds, data });
  },
};
