import { apiClient } from './client';
import { Product, ProductVariant, SourceLink, ApiResponse, PaginatedResponse, ProductFilters } from '@/types';

export interface ProductWithVariants extends Product {
  variants: ProductVariant[];
  totalVariants: number;
  activeVariants: number;
  totalStock: number;
  minPrice: number;
  maxPrice: number;
}

export interface ProductVariantWithSources extends ProductVariant {
  sourceLinks: SourceLink[];
  product: Product;
}

export interface ProductStats {
  totalProducts: number;
  activeProducts: number;
  archivedProducts: number;
  discontinuedProducts: number;
  totalVariants: number;
  totalCategories: number;
  lowStockVariants: number;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  productCount: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Product management API functions
export const productApi = {
  // Get paginated list of products with filters
  async getProducts(filters: ProductFilters): Promise<ApiResponse<PaginatedResponse<ProductWithVariants>>> {
    return apiClient.get('/api/admin/products', filters);
  },

  // Get detailed product information
  async getProductById(productId: string): Promise<ApiResponse<ProductWithVariants>> {
    return apiClient.get(`/api/admin/products/${productId}`);
  },

  // Create new product
  async createProduct(data: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Product>> {
    return apiClient.post('/api/admin/products', data);
  },

  // Update product
  async updateProduct(productId: string, data: Partial<Product>): Promise<ApiResponse<Product>> {
    return apiClient.put(`/api/admin/products/${productId}`, data);
  },

  // Delete product (soft delete)
  async deleteProduct(productId: string): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/admin/products/${productId}`);
  },

  // Upload product images
  async uploadProductImages(productId: string, files: File[]): Promise<ApiResponse<{ imageUrls: string[] }>> {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`images`, file);
    });
    formData.append('productId', productId);

    return apiClient.uploadFile(`/api/admin/products/${productId}/images`, files[0], {
      productId,
      imageCount: files.length.toString(),
    });
  },

  // Get product variants
  async getProductVariants(productId: string): Promise<ApiResponse<ProductVariantWithSources[]>> {
    return apiClient.get(`/api/admin/products/${productId}/variants`);
  },

  // Create product variant
  async createProductVariant(productId: string, data: Omit<ProductVariant, 'id' | 'productId'>): Promise<ApiResponse<ProductVariant>> {
    return apiClient.post(`/api/admin/products/${productId}/variants`, data);
  },

  // Update product variant
  async updateProductVariant(variantId: string, data: Partial<ProductVariant>): Promise<ApiResponse<ProductVariant>> {
    return apiClient.put(`/api/admin/variants/${variantId}`, data);
  },

  // Delete product variant
  async deleteProductVariant(variantId: string): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/admin/variants/${variantId}`);
  },

  // Get variant source links
  async getVariantSourceLinks(variantId: string): Promise<ApiResponse<SourceLink[]>> {
    return apiClient.get(`/api/admin/variants/${variantId}/sources`);
  },

  // Create source link
  async createSourceLink(variantId: string, data: Omit<SourceLink, 'id' | 'productVariantId'>): Promise<ApiResponse<SourceLink>> {
    return apiClient.post(`/api/admin/variants/${variantId}/sources`, data);
  },

  // Update source link
  async updateSourceLink(sourceLinkId: string, data: Partial<SourceLink>): Promise<ApiResponse<SourceLink>> {
    return apiClient.put(`/api/admin/sources/${sourceLinkId}`, data);
  },

  // Delete source link
  async deleteSourceLink(sourceLinkId: string): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/admin/sources/${sourceLinkId}`);
  },

  // Get categories
  async getCategories(): Promise<ApiResponse<Category[]>> {
    return apiClient.get('/api/admin/categories');
  },

  // Create category
  async createCategory(data: Omit<Category, 'id' | 'productCount' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Category>> {
    return apiClient.post('/api/admin/categories', data);
  },

  // Update category
  async updateCategory(categoryId: string, data: Partial<Category>): Promise<ApiResponse<Category>> {
    return apiClient.put(`/api/admin/categories/${categoryId}`, data);
  },

  // Delete category
  async deleteCategory(categoryId: string): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/admin/categories/${categoryId}`);
  },

  // Get product statistics
  async getProductStats(): Promise<ApiResponse<ProductStats>> {
    return apiClient.get('/api/admin/products/stats');
  },

  // Search products
  async searchProducts(query: string, limit: number = 10): Promise<ApiResponse<ProductWithVariants[]>> {
    return apiClient.get('/api/admin/products/search', { query, limit });
  },

  // Bulk update products
  async bulkUpdateProducts(productIds: string[], data: Partial<Product>): Promise<ApiResponse<void>> {
    return apiClient.put('/api/admin/products/bulk', { productIds, data });
  },

  // Export products
  async exportProducts(filters: ProductFilters): Promise<ApiResponse<{ downloadUrl: string }>> {
    return apiClient.post('/api/admin/products/export', filters);
  },

  // Import products
  async importProducts(file: File): Promise<ApiResponse<{ imported: number; errors: string[] }>> {
    return apiClient.uploadFile('/api/admin/products/import', file);
  },

  // Duplicate product
  async duplicateProduct(productId: string): Promise<ApiResponse<Product>> {
    return apiClient.post(`/api/admin/products/${productId}/duplicate`);
  },

  // Get low stock variants
  async getLowStockVariants(threshold: number = 10): Promise<ApiResponse<ProductVariantWithSources[]>> {
    return apiClient.get('/api/admin/variants/low-stock', { threshold });
  },

  // Update variant stock
  async updateVariantStock(variantId: string, stock: number): Promise<ApiResponse<ProductVariant>> {
    return apiClient.put(`/api/admin/variants/${variantId}/stock`, { virtualStock: stock });
  },

  // Reindex products for search
  async reindexProducts(): Promise<ApiResponse<void>> {
    return apiClient.post('/api/admin/products/reindex');
  },
};
