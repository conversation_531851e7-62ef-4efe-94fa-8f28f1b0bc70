import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import { 
  groupBuyApi, 
  GroupBuyWithDetails, 
  GroupBuyStats, 
  GroupBuyFilters, 
  CreateGroupBuyData,
  GroupBuyParticipant 
} from '@/lib/api/groupbuys';
import { GroupBuy } from '@/types';
import { SUCCESS_MESSAGES, ERROR_MESSAGES } from '@/constants';

// Query keys
export const groupBuyQueryKeys = {
  all: ['groupbuys'] as const,
  lists: () => [...groupBuyQueryKeys.all, 'list'] as const,
  list: (filters: GroupBuyFilters) => [...groupBuyQueryKeys.lists(), filters] as const,
  details: () => [...groupBuyQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...groupBuyQueryKeys.details(), id] as const,
  participants: (id: string) => [...groupBuyQueryKeys.detail(id), 'participants'] as const,
  metrics: (id: string) => [...groupBuyQueryKeys.detail(id), 'metrics'] as const,
  stats: () => [...groupBuyQueryKeys.all, 'stats'] as const,
  actionRequired: () => [...groupBuyQueryKeys.all, 'action-required'] as const,
  expiring: (hours: number) => [...groupBuyQueryKeys.all, 'expiring', hours] as const,
  search: (query: string) => [...groupBuyQueryKeys.all, 'search', query] as const,
  analytics: (period: string) => [...groupBuyQueryKeys.all, 'analytics', period] as const,
};

// Get group buys list with filters
export function useGroupBuys(filters: GroupBuyFilters) {
  return useQuery({
    queryKey: groupBuyQueryKeys.list(filters),
    queryFn: () => groupBuyApi.getGroupBuys(filters),
    keepPreviousData: true,
    staleTime: 30000, // 30 seconds
  });
}

// Get group buy details
export function useGroupBuy(groupBuyId: string) {
  return useQuery({
    queryKey: groupBuyQueryKeys.detail(groupBuyId),
    queryFn: () => groupBuyApi.getGroupBuyById(groupBuyId),
    enabled: !!groupBuyId,
    staleTime: 60000, // 1 minute
  });
}

// Get group buy participants
export function useGroupBuyParticipants(groupBuyId: string) {
  return useQuery({
    queryKey: groupBuyQueryKeys.participants(groupBuyId),
    queryFn: () => groupBuyApi.getGroupBuyParticipants(groupBuyId),
    enabled: !!groupBuyId,
    staleTime: 60000, // 1 minute
  });
}

// Get group buy metrics
export function useGroupBuyMetrics(groupBuyId: string) {
  return useQuery({
    queryKey: groupBuyQueryKeys.metrics(groupBuyId),
    queryFn: () => groupBuyApi.getGroupBuyMetrics(groupBuyId),
    enabled: !!groupBuyId,
    staleTime: 300000, // 5 minutes
  });
}

// Get group buy statistics
export function useGroupBuyStats(dateRange?: { from: Date; to: Date }) {
  return useQuery({
    queryKey: [...groupBuyQueryKeys.stats(), dateRange],
    queryFn: () => groupBuyApi.getGroupBuyStats(dateRange),
    staleTime: 300000, // 5 minutes
    refetchInterval: 300000, // Refetch every 5 minutes
  });
}

// Get action required group buys
export function useActionRequiredGroupBuys() {
  return useQuery({
    queryKey: groupBuyQueryKeys.actionRequired(),
    queryFn: () => groupBuyApi.getActionRequiredGroupBuys(),
    staleTime: 60000, // 1 minute
    refetchInterval: 60000, // Refetch every minute
  });
}

// Get expiring group buys
export function useExpiringGroupBuys(hours: number = 24) {
  return useQuery({
    queryKey: groupBuyQueryKeys.expiring(hours),
    queryFn: () => groupBuyApi.getExpiringGroupBuys(hours),
    staleTime: 60000, // 1 minute
    refetchInterval: 300000, // Refetch every 5 minutes
  });
}

// Search group buys
export function useGroupBuySearch(query: string, enabled: boolean = true) {
  return useQuery({
    queryKey: groupBuyQueryKeys.search(query),
    queryFn: () => groupBuyApi.searchGroupBuys(query),
    enabled: enabled && query.length > 2,
    staleTime: 30000, // 30 seconds
  });
}

// Get group buy analytics
export function useGroupBuyAnalytics(period: 'day' | 'week' | 'month' | 'year') {
  return useQuery({
    queryKey: groupBuyQueryKeys.analytics(period),
    queryFn: () => groupBuyApi.getGroupBuyAnalytics(period),
    staleTime: 300000, // 5 minutes
  });
}

// Create group buy mutation
export function useCreateGroupBuy() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateGroupBuyData) => groupBuyApi.createGroupBuy(data),
    onSuccess: () => {
      message.success(SUCCESS_MESSAGES.CREATED);
      queryClient.invalidateQueries({ queryKey: groupBuyQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: groupBuyQueryKeys.stats() });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Update group buy mutation
export function useUpdateGroupBuy() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ groupBuyId, data }: { groupBuyId: string; data: Partial<GroupBuy> }) =>
      groupBuyApi.updateGroupBuy(groupBuyId, data),
    onSuccess: (response, { groupBuyId }) => {
      message.success(SUCCESS_MESSAGES.UPDATED);
      queryClient.invalidateQueries({ queryKey: groupBuyQueryKeys.detail(groupBuyId) });
      queryClient.invalidateQueries({ queryKey: groupBuyQueryKeys.lists() });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Update group buy status mutation
export function useUpdateGroupBuyStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ groupBuyId, status, notes }: { groupBuyId: string; status: string; notes?: string }) =>
      groupBuyApi.updateGroupBuyStatus(groupBuyId, status, notes),
    onSuccess: (response, { groupBuyId }) => {
      message.success(SUCCESS_MESSAGES.UPDATED);
      queryClient.invalidateQueries({ queryKey: groupBuyQueryKeys.detail(groupBuyId) });
      queryClient.invalidateQueries({ queryKey: groupBuyQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: groupBuyQueryKeys.stats() });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Delete group buy mutation
export function useDeleteGroupBuy() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (groupBuyId: string) => groupBuyApi.deleteGroupBuy(groupBuyId),
    onSuccess: () => {
      message.success(SUCCESS_MESSAGES.DELETED);
      queryClient.invalidateQueries({ queryKey: groupBuyQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: groupBuyQueryKeys.stats() });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Remove participant mutation
export function useRemoveParticipant() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ groupBuyId, participantId, reason }: { groupBuyId: string; participantId: string; reason?: string }) =>
      groupBuyApi.removeParticipant(groupBuyId, participantId, reason),
    onSuccess: (response, { groupBuyId }) => {
      message.success('Participant removed successfully');
      queryClient.invalidateQueries({ queryKey: groupBuyQueryKeys.participants(groupBuyId) });
      queryClient.invalidateQueries({ queryKey: groupBuyQueryKeys.detail(groupBuyId) });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Add participant mutation
export function useAddParticipant() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ groupBuyId, data }: { groupBuyId: string; data: any }) =>
      groupBuyApi.addParticipant(groupBuyId, data),
    onSuccess: (response, { groupBuyId }) => {
      message.success('Participant added successfully');
      queryClient.invalidateQueries({ queryKey: groupBuyQueryKeys.participants(groupBuyId) });
      queryClient.invalidateQueries({ queryKey: groupBuyQueryKeys.detail(groupBuyId) });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Extend deadline mutation
export function useExtendDeadline() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ groupBuyId, newExpiresAt, reason }: { groupBuyId: string; newExpiresAt: Date; reason?: string }) =>
      groupBuyApi.extendDeadline(groupBuyId, newExpiresAt, reason),
    onSuccess: (response, { groupBuyId }) => {
      message.success('Deadline extended successfully');
      queryClient.invalidateQueries({ queryKey: groupBuyQueryKeys.detail(groupBuyId) });
      queryClient.invalidateQueries({ queryKey: groupBuyQueryKeys.lists() });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Force complete mutation
export function useForceComplete() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ groupBuyId, reason }: { groupBuyId: string; reason: string }) =>
      groupBuyApi.forceComplete(groupBuyId, reason),
    onSuccess: (response, { groupBuyId }) => {
      message.success('Group buy completed successfully');
      queryClient.invalidateQueries({ queryKey: groupBuyQueryKeys.detail(groupBuyId) });
      queryClient.invalidateQueries({ queryKey: groupBuyQueryKeys.lists() });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Mark fulfilled mutation
export function useMarkFulfilled() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ groupBuyId, data }: { groupBuyId: string; data: any }) =>
      groupBuyApi.markFulfilled(groupBuyId, data),
    onSuccess: (response, { groupBuyId }) => {
      message.success('Group buy marked as fulfilled');
      queryClient.invalidateQueries({ queryKey: groupBuyQueryKeys.detail(groupBuyId) });
      queryClient.invalidateQueries({ queryKey: groupBuyQueryKeys.lists() });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Duplicate group buy mutation
export function useDuplicateGroupBuy() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ groupBuyId, data }: { groupBuyId: string; data: any }) =>
      groupBuyApi.duplicateGroupBuy(groupBuyId, data),
    onSuccess: () => {
      message.success('Group buy duplicated successfully');
      queryClient.invalidateQueries({ queryKey: groupBuyQueryKeys.lists() });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Notify participants mutation
export function useNotifyParticipants() {
  return useMutation({
    mutationFn: ({ groupBuyId, data }: { groupBuyId: string; data: any }) =>
      groupBuyApi.notifyParticipants(groupBuyId, data),
    onSuccess: () => {
      message.success('Notification sent to participants');
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Export group buys mutation
export function useExportGroupBuys() {
  return useMutation({
    mutationFn: (filters: GroupBuyFilters) => groupBuyApi.exportGroupBuys(filters),
    onSuccess: (response) => {
      message.success('Export started. Download will begin shortly.');
      if (response.data.downloadUrl) {
        window.open(response.data.downloadUrl, '_blank');
      }
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Bulk update group buys mutation
export function useBulkUpdateGroupBuys() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ groupBuyIds, data }: { groupBuyIds: string[]; data: any }) =>
      groupBuyApi.bulkUpdateGroupBuys(groupBuyIds, data),
    onSuccess: (response, { groupBuyIds }) => {
      message.success(`Successfully updated ${groupBuyIds.length} group buys`);
      queryClient.invalidateQueries({ queryKey: groupBuyQueryKeys.lists() });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}
