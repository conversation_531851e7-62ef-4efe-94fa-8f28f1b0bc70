import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import { orderApi, OrderWithDetails, OrderStats, OrderTimeline, PaymentInfo, RefundRequest } from '@/lib/api/orders';
import { OrderFilters, Order, OrderItem, OrderIssue } from '@/types';
import { SUCCESS_MESSAGES, ERROR_MESSAGES } from '@/constants';

// Query keys
export const orderQueryKeys = {
  all: ['orders'] as const,
  lists: () => [...orderQueryKeys.all, 'list'] as const,
  list: (filters: OrderFilters) => [...orderQueryKeys.lists(), filters] as const,
  details: () => [...orderQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...orderQueryKeys.details(), id] as const,
  timeline: (id: string) => [...orderQueryKeys.detail(id), 'timeline'] as const,
  paymentInfo: (id: string) => [...orderQueryKeys.detail(id), 'payment-info'] as const,
  fulfillmentSummary: (id: string) => [...orderQueryKeys.detail(id), 'fulfillment-summary'] as const,
  stats: () => [...orderQueryKeys.all, 'stats'] as const,
  actionRequired: () => [...orderQueryKeys.all, 'action-required'] as const,
  pendingPayment: () => [...orderQueryKeys.all, 'pending-payment'] as const,
  search: (query: string) => [...orderQueryKeys.all, 'search', query] as const,
  analytics: (period: string) => [...orderQueryKeys.all, 'analytics', period] as const,
};

// Get orders list with filters
export function useOrders(filters: OrderFilters) {
  return useQuery({
    queryKey: orderQueryKeys.list(filters),
    queryFn: () => orderApi.getOrders(filters),
    keepPreviousData: true,
    staleTime: 30000, // 30 seconds
  });
}

// Get order details
export function useOrder(orderId: string) {
  return useQuery({
    queryKey: orderQueryKeys.detail(orderId),
    queryFn: () => orderApi.getOrderById(orderId),
    enabled: !!orderId,
    staleTime: 60000, // 1 minute
  });
}

// Get order timeline
export function useOrderTimeline(orderId: string) {
  return useQuery({
    queryKey: orderQueryKeys.timeline(orderId),
    queryFn: () => orderApi.getOrderTimeline(orderId),
    enabled: !!orderId,
    staleTime: 60000, // 1 minute
  });
}

// Get payment info
export function usePaymentInfo(orderId: string) {
  return useQuery({
    queryKey: orderQueryKeys.paymentInfo(orderId),
    queryFn: () => orderApi.getPaymentInfo(orderId),
    enabled: !!orderId,
    staleTime: 300000, // 5 minutes
  });
}

// Get fulfillment summary
export function useOrderFulfillmentSummary(orderId: string) {
  return useQuery({
    queryKey: orderQueryKeys.fulfillmentSummary(orderId),
    queryFn: () => orderApi.getOrderFulfillmentSummary(orderId),
    enabled: !!orderId,
    staleTime: 60000, // 1 minute
  });
}

// Get order statistics
export function useOrderStats(dateRange?: { from: Date; to: Date }) {
  return useQuery({
    queryKey: [...orderQueryKeys.stats(), dateRange],
    queryFn: () => orderApi.getOrderStats(dateRange),
    staleTime: 300000, // 5 minutes
    refetchInterval: 300000, // Refetch every 5 minutes
  });
}

// Get action required orders
export function useActionRequiredOrders() {
  return useQuery({
    queryKey: orderQueryKeys.actionRequired(),
    queryFn: () => orderApi.getActionRequiredOrders(),
    staleTime: 60000, // 1 minute
    refetchInterval: 60000, // Refetch every minute
  });
}

// Get pending payment orders
export function usePendingPaymentOrders() {
  return useQuery({
    queryKey: orderQueryKeys.pendingPayment(),
    queryFn: () => orderApi.getPendingPaymentOrders(),
    staleTime: 60000, // 1 minute
    refetchInterval: 60000, // Refetch every minute
  });
}

// Search orders
export function useOrderSearch(query: string, enabled: boolean = true) {
  return useQuery({
    queryKey: orderQueryKeys.search(query),
    queryFn: () => orderApi.searchOrders(query),
    enabled: enabled && query.length > 2,
    staleTime: 30000, // 30 seconds
  });
}

// Get order analytics
export function useOrderAnalytics(period: 'day' | 'week' | 'month' | 'year') {
  return useQuery({
    queryKey: orderQueryKeys.analytics(period),
    queryFn: () => orderApi.getOrderAnalytics(period),
    staleTime: 300000, // 5 minutes
  });
}

// Update order status mutation
export function useUpdateOrderStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ orderId, status, notes }: { orderId: string; status: string; notes?: string }) =>
      orderApi.updateOrderStatus(orderId, status, notes),
    onSuccess: (response, { orderId }) => {
      message.success(SUCCESS_MESSAGES.UPDATED);
      queryClient.invalidateQueries({ queryKey: orderQueryKeys.detail(orderId) });
      queryClient.invalidateQueries({ queryKey: orderQueryKeys.timeline(orderId) });
      queryClient.invalidateQueries({ queryKey: orderQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: orderQueryKeys.stats() });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Create order issue mutation
export function useCreateOrderIssue() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ orderId, data }: { orderId: string; data: any }) =>
      orderApi.createOrderIssue(orderId, data),
    onSuccess: (response, { orderId }) => {
      message.success(SUCCESS_MESSAGES.CREATED);
      queryClient.invalidateQueries({ queryKey: orderQueryKeys.detail(orderId) });
      queryClient.invalidateQueries({ queryKey: orderQueryKeys.actionRequired() });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Resolve order issue mutation
export function useResolveOrderIssue() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ issueId, resolution, notes }: { issueId: string; resolution: string; notes?: string }) =>
      orderApi.resolveOrderIssue(issueId, resolution, notes),
    onSuccess: () => {
      message.success('Order issue resolved successfully');
      queryClient.invalidateQueries({ queryKey: orderQueryKeys.all });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Verify payment mutation
export function useVerifyPayment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ orderId, data }: { orderId: string; data: any }) =>
      orderApi.verifyPayment(orderId, data),
    onSuccess: (response, { orderId }) => {
      message.success('Payment verification completed');
      queryClient.invalidateQueries({ queryKey: orderQueryKeys.detail(orderId) });
      queryClient.invalidateQueries({ queryKey: orderQueryKeys.pendingPayment() });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Process refund mutation
export function useProcessRefund() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: RefundRequest) => orderApi.processRefund(data),
    onSuccess: (response, data) => {
      message.success('Refund processed successfully');
      queryClient.invalidateQueries({ queryKey: orderQueryKeys.detail(data.orderId) });
      queryClient.invalidateQueries({ queryKey: orderQueryKeys.lists() });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Cancel order mutation
export function useCancelOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ orderId, reason }: { orderId: string; reason: string }) =>
      orderApi.cancelOrder(orderId, reason),
    onSuccess: (response, { orderId }) => {
      message.success('Order cancelled successfully');
      queryClient.invalidateQueries({ queryKey: orderQueryKeys.detail(orderId) });
      queryClient.invalidateQueries({ queryKey: orderQueryKeys.lists() });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Update item fulfillment status mutation
export function useUpdateItemFulfillmentStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ orderItemId, status, notes }: { orderItemId: string; status: string; notes?: string }) =>
      orderApi.updateItemFulfillmentStatus(orderItemId, status, notes),
    onSuccess: () => {
      message.success(SUCCESS_MESSAGES.UPDATED);
      queryClient.invalidateQueries({ queryKey: orderQueryKeys.all });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Add tracking info mutation
export function useAddTrackingInfo() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ orderId, data }: { orderId: string; data: any }) =>
      orderApi.addTrackingInfo(orderId, data),
    onSuccess: (response, { orderId }) => {
      message.success('Tracking information added successfully');
      queryClient.invalidateQueries({ queryKey: orderQueryKeys.detail(orderId) });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Export orders mutation
export function useExportOrders() {
  return useMutation({
    mutationFn: (filters: OrderFilters) => orderApi.exportOrders(filters),
    onSuccess: (response) => {
      message.success('Export started. Download will begin shortly.');
      if (response.data.downloadUrl) {
        window.open(response.data.downloadUrl, '_blank');
      }
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Bulk update orders mutation
export function useBulkUpdateOrders() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ orderIds, data }: { orderIds: string[]; data: any }) =>
      orderApi.bulkUpdateOrders(orderIds, data),
    onSuccess: (response, { orderIds }) => {
      message.success(`Successfully updated ${orderIds.length} orders`);
      queryClient.invalidateQueries({ queryKey: orderQueryKeys.lists() });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Update order notes mutation
export function useUpdateOrderNotes() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ orderId, notes }: { orderId: string; notes: string }) =>
      orderApi.updateOrderNotes(orderId, notes),
    onSuccess: (response, { orderId }) => {
      message.success(SUCCESS_MESSAGES.UPDATED);
      queryClient.invalidateQueries({ queryKey: orderQueryKeys.detail(orderId) });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}
