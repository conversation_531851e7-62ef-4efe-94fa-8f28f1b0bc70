import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import { productApi, ProductWithVariants, ProductVariantWithSources, ProductStats, Category } from '@/lib/api/products';
import { ProductFilters, Product, ProductVariant, SourceLink } from '@/types';
import { SUCCESS_MESSAGES, ERROR_MESSAGES } from '@/constants';

// Query keys
export const productQueryKeys = {
  all: ['products'] as const,
  lists: () => [...productQueryKeys.all, 'list'] as const,
  list: (filters: ProductFilters) => [...productQueryKeys.lists(), filters] as const,
  details: () => [...productQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...productQueryKeys.details(), id] as const,
  variants: (id: string) => [...productQueryKeys.detail(id), 'variants'] as const,
  variant: (id: string) => [...productQueryKeys.all, 'variant', id] as const,
  sources: (variantId: string) => [...productQueryKeys.variant(variantId), 'sources'] as const,
  categories: () => [...productQueryKeys.all, 'categories'] as const,
  stats: () => [...productQueryKeys.all, 'stats'] as const,
  search: (query: string) => [...productQueryKeys.all, 'search', query] as const,
  lowStock: (threshold: number) => [...productQueryKeys.all, 'low-stock', threshold] as const,
};

// Get products list with filters
export function useProducts(filters: ProductFilters) {
  return useQuery({
    queryKey: productQueryKeys.list(filters),
    queryFn: () => productApi.getProducts(filters),
    keepPreviousData: true,
    staleTime: 30000, // 30 seconds
  });
}

// Get product details
export function useProduct(productId: string) {
  return useQuery({
    queryKey: productQueryKeys.detail(productId),
    queryFn: () => productApi.getProductById(productId),
    enabled: !!productId,
    staleTime: 60000, // 1 minute
  });
}

// Get product variants
export function useProductVariants(productId: string) {
  return useQuery({
    queryKey: productQueryKeys.variants(productId),
    queryFn: () => productApi.getProductVariants(productId),
    enabled: !!productId,
    staleTime: 60000, // 1 minute
  });
}

// Get variant source links
export function useVariantSourceLinks(variantId: string) {
  return useQuery({
    queryKey: productQueryKeys.sources(variantId),
    queryFn: () => productApi.getVariantSourceLinks(variantId),
    enabled: !!variantId,
    staleTime: 300000, // 5 minutes
  });
}

// Get categories
export function useCategories() {
  return useQuery({
    queryKey: productQueryKeys.categories(),
    queryFn: () => productApi.getCategories(),
    staleTime: 300000, // 5 minutes
  });
}

// Get product statistics
export function useProductStats() {
  return useQuery({
    queryKey: productQueryKeys.stats(),
    queryFn: () => productApi.getProductStats(),
    staleTime: 300000, // 5 minutes
    refetchInterval: 300000, // Refetch every 5 minutes
  });
}

// Search products
export function useProductSearch(query: string, enabled: boolean = true) {
  return useQuery({
    queryKey: productQueryKeys.search(query),
    queryFn: () => productApi.searchProducts(query),
    enabled: enabled && query.length > 2,
    staleTime: 30000, // 30 seconds
  });
}

// Get low stock variants
export function useLowStockVariants(threshold: number = 10) {
  return useQuery({
    queryKey: productQueryKeys.lowStock(threshold),
    queryFn: () => productApi.getLowStockVariants(threshold),
    staleTime: 300000, // 5 minutes
  });
}

// Create product mutation
export function useCreateProduct() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) =>
      productApi.createProduct(data),
    onSuccess: () => {
      message.success(SUCCESS_MESSAGES.CREATED);
      queryClient.invalidateQueries({ queryKey: productQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: productQueryKeys.stats() });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Update product mutation
export function useUpdateProduct() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productId, data }: { productId: string; data: Partial<Product> }) =>
      productApi.updateProduct(productId, data),
    onSuccess: (response, { productId }) => {
      message.success(SUCCESS_MESSAGES.UPDATED);
      queryClient.invalidateQueries({ queryKey: productQueryKeys.detail(productId) });
      queryClient.invalidateQueries({ queryKey: productQueryKeys.lists() });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Delete product mutation
export function useDeleteProduct() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (productId: string) => productApi.deleteProduct(productId),
    onSuccess: () => {
      message.success(SUCCESS_MESSAGES.DELETED);
      queryClient.invalidateQueries({ queryKey: productQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: productQueryKeys.stats() });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Upload product images mutation
export function useUploadProductImages() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productId, files }: { productId: string; files: File[] }) =>
      productApi.uploadProductImages(productId, files),
    onSuccess: (response, { productId }) => {
      message.success(SUCCESS_MESSAGES.UPLOADED);
      queryClient.invalidateQueries({ queryKey: productQueryKeys.detail(productId) });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Create product variant mutation
export function useCreateProductVariant() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productId, data }: { productId: string; data: Omit<ProductVariant, 'id' | 'productId'> }) =>
      productApi.createProductVariant(productId, data),
    onSuccess: (response, { productId }) => {
      message.success(SUCCESS_MESSAGES.CREATED);
      queryClient.invalidateQueries({ queryKey: productQueryKeys.variants(productId) });
      queryClient.invalidateQueries({ queryKey: productQueryKeys.detail(productId) });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Update product variant mutation
export function useUpdateProductVariant() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ variantId, data }: { variantId: string; data: Partial<ProductVariant> }) =>
      productApi.updateProductVariant(variantId, data),
    onSuccess: () => {
      message.success(SUCCESS_MESSAGES.UPDATED);
      queryClient.invalidateQueries({ queryKey: productQueryKeys.all });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Delete product variant mutation
export function useDeleteProductVariant() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (variantId: string) => productApi.deleteProductVariant(variantId),
    onSuccess: () => {
      message.success(SUCCESS_MESSAGES.DELETED);
      queryClient.invalidateQueries({ queryKey: productQueryKeys.all });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Create source link mutation
export function useCreateSourceLink() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ variantId, data }: { variantId: string; data: Omit<SourceLink, 'id' | 'productVariantId'> }) =>
      productApi.createSourceLink(variantId, data),
    onSuccess: (response, { variantId }) => {
      message.success(SUCCESS_MESSAGES.CREATED);
      queryClient.invalidateQueries({ queryKey: productQueryKeys.sources(variantId) });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Update source link mutation
export function useUpdateSourceLink() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ sourceLinkId, data }: { sourceLinkId: string; data: Partial<SourceLink> }) =>
      productApi.updateSourceLink(sourceLinkId, data),
    onSuccess: () => {
      message.success(SUCCESS_MESSAGES.UPDATED);
      queryClient.invalidateQueries({ queryKey: productQueryKeys.all });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Delete source link mutation
export function useDeleteSourceLink() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (sourceLinkId: string) => productApi.deleteSourceLink(sourceLinkId),
    onSuccess: () => {
      message.success(SUCCESS_MESSAGES.DELETED);
      queryClient.invalidateQueries({ queryKey: productQueryKeys.all });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Create category mutation
export function useCreateCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Omit<Category, 'id' | 'productCount' | 'createdAt' | 'updatedAt'>) =>
      productApi.createCategory(data),
    onSuccess: () => {
      message.success(SUCCESS_MESSAGES.CREATED);
      queryClient.invalidateQueries({ queryKey: productQueryKeys.categories() });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Bulk update products mutation
export function useBulkUpdateProducts() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productIds, data }: { productIds: string[]; data: Partial<Product> }) =>
      productApi.bulkUpdateProducts(productIds, data),
    onSuccess: () => {
      message.success(`Successfully updated ${arguments[0].productIds.length} products`);
      queryClient.invalidateQueries({ queryKey: productQueryKeys.lists() });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Export products mutation
export function useExportProducts() {
  return useMutation({
    mutationFn: (filters: ProductFilters) => productApi.exportProducts(filters),
    onSuccess: (response) => {
      message.success('Export started. Download will begin shortly.');
      if (response.data.downloadUrl) {
        window.open(response.data.downloadUrl, '_blank');
      }
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}

// Import products mutation
export function useImportProducts() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (file: File) => productApi.importProducts(file),
    onSuccess: (response) => {
      message.success(`Successfully imported ${response.data.imported} products`);
      if (response.data.errors.length > 0) {
        message.warning(`${response.data.errors.length} items had errors`);
      }
      queryClient.invalidateQueries({ queryKey: productQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: productQueryKeys.stats() });
    },
    onError: (error: any) => {
      message.error(error.message || ERROR_MESSAGES.GENERIC);
    },
  });
}
