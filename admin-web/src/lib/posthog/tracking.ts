import { trackEvent, POSTHOG_EVENTS, POSTHOG_PROPERTIES } from './config';

// User Management Tracking
export const trackUserManagement = {
  userViewed: (userId: string, userEmail: string) => {
    trackEvent(POSTHOG_EVENTS.USER_VIEWED, {
      [POSTHOG_PROPERTIES.ENTITY_ID]: userId,
      user_email: userEmail,
    });
  },

  userUpdated: (userId: string, changes: Record<string, any>) => {
    trackEvent(POSTHOG_EVENTS.USER_UPDATED, {
      [POSTHOG_PROPERTIES.ENTITY_ID]: userId,
      changes: Object.keys(changes),
      change_count: Object.keys(changes).length,
    });
  },

  userStatusChanged: (userId: string, oldStatus: string, newStatus: string) => {
    trackEvent(POSTHOG_EVENTS.USER_STATUS_CHANGED, {
      [POSTHOG_PROPERTIES.ENTITY_ID]: userId,
      old_status: oldStatus,
      new_status: newStatus,
    });
  },

  usersExported: (count: number, filters: Record<string, any>) => {
    trackEvent(POSTHOG_EVENTS.USER_EXPORTED, {
      [POSTHOG_PROPERTIES.ENTITY_COUNT]: count,
      filters,
    });
  },
};

// Product Management Tracking
export const trackProductManagement = {
  productCreated: (productId: string, category: string) => {
    trackEvent(POSTHOG_EVENTS.PRODUCT_CREATED, {
      [POSTHOG_PROPERTIES.ENTITY_ID]: productId,
      category,
    });
  },

  productUpdated: (productId: string, changes: Record<string, any>) => {
    trackEvent(POSTHOG_EVENTS.PRODUCT_UPDATED, {
      [POSTHOG_PROPERTIES.ENTITY_ID]: productId,
      changes: Object.keys(changes),
      change_count: Object.keys(changes).length,
    });
  },

  productDeleted: (productId: string, category: string) => {
    trackEvent(POSTHOG_EVENTS.PRODUCT_DELETED, {
      [POSTHOG_PROPERTIES.ENTITY_ID]: productId,
      category,
    });
  },

  variantCreated: (variantId: string, productId: string) => {
    trackEvent(POSTHOG_EVENTS.PRODUCT_VARIANT_CREATED, {
      [POSTHOG_PROPERTIES.ENTITY_ID]: variantId,
      product_id: productId,
    });
  },

  productsImported: (count: number, errors: number) => {
    trackEvent(POSTHOG_EVENTS.PRODUCTS_IMPORTED, {
      [POSTHOG_PROPERTIES.ENTITY_COUNT]: count,
      error_count: errors,
      success_rate: ((count - errors) / count) * 100,
    });
  },
};

// Order Management Tracking
export const trackOrderManagement = {
  orderViewed: (orderId: string, orderValue: number, currency: string) => {
    trackEvent(POSTHOG_EVENTS.ORDER_VIEWED, {
      [POSTHOG_PROPERTIES.ENTITY_ID]: orderId,
      [POSTHOG_PROPERTIES.ORDER_VALUE]: orderValue,
      [POSTHOG_PROPERTIES.ORDER_CURRENCY]: currency,
    });
  },

  orderStatusUpdated: (orderId: string, oldStatus: string, newStatus: string) => {
    trackEvent(POSTHOG_EVENTS.ORDER_STATUS_UPDATED, {
      [POSTHOG_PROPERTIES.ENTITY_ID]: orderId,
      old_status: oldStatus,
      new_status: newStatus,
    });
  },

  orderIssueCreated: (orderId: string, issueType: string) => {
    trackEvent(POSTHOG_EVENTS.ORDER_ISSUE_CREATED, {
      [POSTHOG_PROPERTIES.ENTITY_ID]: orderId,
      issue_type: issueType,
    });
  },

  paymentVerified: (orderId: string, paymentMethod: string, amount: number) => {
    trackEvent(POSTHOG_EVENTS.PAYMENT_VERIFIED, {
      [POSTHOG_PROPERTIES.ENTITY_ID]: orderId,
      payment_method: paymentMethod,
      amount,
    });
  },

  refundProcessed: (orderId: string, refundAmount: number, reason: string) => {
    trackEvent(POSTHOG_EVENTS.ORDER_REFUND_PROCESSED, {
      [POSTHOG_PROPERTIES.ENTITY_ID]: orderId,
      refund_amount: refundAmount,
      reason,
    });
  },
};

// Group Buy Management Tracking
export const trackGroupBuyManagement = {
  groupBuyCreated: (groupBuyId: string, productId: string, targetQuantity: number) => {
    trackEvent(POSTHOG_EVENTS.GROUP_BUY_CREATED, {
      [POSTHOG_PROPERTIES.ENTITY_ID]: groupBuyId,
      product_id: productId,
      [POSTHOG_PROPERTIES.GROUP_BUY_TARGET]: targetQuantity,
    });
  },

  groupBuyStatusChanged: (groupBuyId: string, oldStatus: string, newStatus: string, progress: number) => {
    trackEvent(POSTHOG_EVENTS.GROUP_BUY_STATUS_CHANGED, {
      [POSTHOG_PROPERTIES.ENTITY_ID]: groupBuyId,
      old_status: oldStatus,
      new_status: newStatus,
      [POSTHOG_PROPERTIES.GROUP_BUY_PROGRESS]: progress,
    });
  },

  groupBuyDeadlineExtended: (groupBuyId: string, originalDate: Date, newDate: Date) => {
    trackEvent(POSTHOG_EVENTS.GROUP_BUY_DEADLINE_EXTENDED, {
      [POSTHOG_PROPERTIES.ENTITY_ID]: groupBuyId,
      original_date: originalDate.toISOString(),
      new_date: newDate.toISOString(),
      extension_hours: (newDate.getTime() - originalDate.getTime()) / (1000 * 60 * 60),
    });
  },

  participantAdded: (groupBuyId: string, userId: string, quantity: number) => {
    trackEvent(POSTHOG_EVENTS.GROUP_BUY_PARTICIPANT_ADDED, {
      [POSTHOG_PROPERTIES.ENTITY_ID]: groupBuyId,
      user_id: userId,
      quantity,
    });
  },

  notificationSent: (groupBuyId: string, participantCount: number, notificationType: string) => {
    trackEvent(POSTHOG_EVENTS.GROUP_BUY_NOTIFICATION_SENT, {
      [POSTHOG_PROPERTIES.ENTITY_ID]: groupBuyId,
      participant_count: participantCount,
      notification_type: notificationType,
    });
  },
};

// Analytics Tracking
export const trackAnalytics = {
  dashboardViewed: (section: string, filters?: Record<string, any>) => {
    trackEvent(POSTHOG_EVENTS.DASHBOARD_VIEWED, {
      section,
      filters: filters || {},
    });
  },

  reportGenerated: (reportType: string, filters: Record<string, any>) => {
    trackEvent(POSTHOG_EVENTS.REPORT_GENERATED, {
      report_type: reportType,
      filters,
    });
  },

  filterApplied: (filterType: string, filterValue: any, context: string) => {
    trackEvent(POSTHOG_EVENTS.FILTER_APPLIED, {
      filter_type: filterType,
      filter_value: filterValue,
      context,
    });
  },

  searchPerformed: (query: string, resultCount: number, context: string) => {
    trackEvent(POSTHOG_EVENTS.SEARCH_PERFORMED, {
      search_query: query,
      result_count: resultCount,
      context,
    });
  },
};

// System Actions Tracking
export const trackSystemActions = {
  bulkActionPerformed: (actionType: string, entityType: string, entityCount: number) => {
    trackEvent(POSTHOG_EVENTS.BULK_ACTION_PERFORMED, {
      [POSTHOG_PROPERTIES.ACTION_TYPE]: actionType,
      [POSTHOG_PROPERTIES.ENTITY_TYPE]: entityType,
      [POSTHOG_PROPERTIES.ENTITY_COUNT]: entityCount,
    });
  },

  exportDownloaded: (exportType: string, recordCount: number) => {
    trackEvent(POSTHOG_EVENTS.EXPORT_DOWNLOADED, {
      export_type: exportType,
      record_count: recordCount,
    });
  },

  importCompleted: (importType: string, successCount: number, errorCount: number) => {
    trackEvent(POSTHOG_EVENTS.IMPORT_COMPLETED, {
      import_type: importType,
      success_count: successCount,
      error_count: errorCount,
      success_rate: (successCount / (successCount + errorCount)) * 100,
    });
  },
};

// Performance Tracking
export const trackPerformance = {
  pageLoadTime: (page: string, loadTime: number) => {
    trackEvent('admin_page_load_time', {
      page,
      [POSTHOG_PROPERTIES.LOAD_TIME]: loadTime,
    });
  },

  apiResponseTime: (endpoint: string, responseTime: number, success: boolean) => {
    trackEvent('admin_api_response_time', {
      endpoint,
      [POSTHOG_PROPERTIES.API_RESPONSE_TIME]: responseTime,
      success,
    });
  },

  errorOccurred: (errorType: string, errorMessage: string, context: string) => {
    trackEvent('admin_error_occurred', {
      [POSTHOG_PROPERTIES.ERROR_TYPE]: errorType,
      [POSTHOG_PROPERTIES.ERROR_MESSAGE]: errorMessage,
      context,
    });
  },
};

// Convenience function to track all admin login
export const trackAdminLogin = (adminId: string, adminEmail: string, isSuperAdmin: boolean) => {
  trackEvent(POSTHOG_EVENTS.ADMIN_LOGIN, {
    [POSTHOG_PROPERTIES.ADMIN_ID]: adminId,
    [POSTHOG_PROPERTIES.ADMIN_EMAIL]: adminEmail,
    [POSTHOG_PROPERTIES.ADMIN_ROLE]: isSuperAdmin ? 'super_admin' : 'admin',
    login_timestamp: new Date().toISOString(),
  });
};

// Convenience function to track admin logout
export const trackAdminLogout = (adminId: string, sessionDuration: number) => {
  trackEvent(POSTHOG_EVENTS.ADMIN_LOGOUT, {
    [POSTHOG_PROPERTIES.ADMIN_ID]: adminId,
    session_duration: sessionDuration,
    logout_timestamp: new Date().toISOString(),
  });
};
