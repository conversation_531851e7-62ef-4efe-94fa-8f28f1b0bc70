[versions]
accompanistPermissions = "0.34.0"
accompanistSystemuicontroller = "0.28.0"
accompanistPager = "0.28.0"
agp = "8.11.0"
animation = "1.8.2"
coilCompose = "2.6.0"
constraintlayoutCompose = "1.1.1"
datastorePreferences = "1.1.7"
firebaseBom = "33.15.0"
gson = "2.12.1"
kotlin = "2.0.21"
coreKtx = "1.16.0"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
lifecycleRuntimeKtx = "2.9.1"
activityCompose = "1.10.1"
composeBom = "2025.06.00"

hilt = "2.51.1"
hiltNavigationCompose = "1.2.0"

googleGmsGoogleServices = "4.4.2"
googleFirebaseCrashlytics = "3.0.4"
googleFirebaseFirebasePerf = "1.4.2"

navigationCompose = "2.9.0"
roomRuntime = "2.7.1"
splashscreen = "1.0.1"

exoplayer = "1.7.1"
playServicesAuth = "21.3.0"
firebaseStorageKtx = "21.0.2"
materialIconsExtended = "1.7.8"

androidImageCropper = "4.5.0"
pagingCommonAndroid = "3.3.6"
translate = "17.0.3"

ksp = "2.0.21-1.0.27"

[libraries]
androidx-material-icons-extended = { module = "androidx.compose.material:material-icons-extended", version.ref = "materialIconsExtended" }
accompanist-permissions = { module = "com.google.accompanist:accompanist-permissions", version.ref = "accompanistPermissions" }
androidx-animation = { module = "androidx.compose.animation:animation", version.ref = "animation" }
accompanist-systemuicontroller = { module = "com.google.accompanist:accompanist-systemuicontroller", version.ref = "accompanistSystemuicontroller" }
accompanist-pager = { module = "com.google.accompanist:accompanist-pager", version.ref = "accompanistPager" }
accompanist-pager-indicators = { module = "com.google.accompanist:accompanist-pager-indicators", version.ref = "accompanistPager" }
androidx-constraintlayout-compose = { module = "androidx.constraintlayout:constraintlayout-compose", version.ref = "constraintlayoutCompose" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-datastore-preferences = { module = "androidx.datastore:datastore-preferences", version.ref = "datastorePreferences" }
androidx-room-compiler = { module = "androidx.room:room-compiler", version.ref = "roomRuntime" }
androidx-room-ktx = { module = "androidx.room:room-ktx", version.ref = "roomRuntime" }
androidx-room-runtime = { module = "androidx.room:room-runtime", version.ref = "roomRuntime" }
coil-compose = { module = "io.coil-kt:coil-compose", version.ref = "coilCompose" }
coil-svg = { module = "io.coil-kt:coil-svg", version.ref = "coilCompose" }
coil-video = { module = "io.coil-kt:coil-video", version.ref = "coilCompose" }
gson = { module = "com.google.code.gson:gson", version.ref = "gson" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-material3 = { group = "androidx.compose.material3", name = "material3" }

androidx-hilt-navigation-compose = { group = "androidx.hilt", name = "hilt-navigation-compose", version.ref = "hiltNavigationCompose" }
hilt-android = { group = "com.google.dagger", name = "hilt-android", version.ref = "hilt" }
hilt-android-compiler = { group = "com.google.dagger", name = "hilt-android-compiler", version.ref = "hilt" }

firebase-bom = { module = "com.google.firebase:firebase-bom", version.ref = "firebaseBom" }
firebase-analytics = { module = "com.google.firebase:firebase-analytics" }
firebase-auth = { module = "com.google.firebase:firebase-auth" }
firebase-firestore = { module = "com.google.firebase:firebase-firestore" }
firebase-crashlytics = { module = "com.google.firebase:firebase-crashlytics" }
firebase-perf = { module = "com.google.firebase:firebase-perf" }
firebase-storage-ktx = { group = "com.google.firebase", name = "firebase-storage-ktx", version.ref = "firebaseStorageKtx" }
firebase-messaging = { module = "com.google.firebase:firebase-messaging" }

core-splashscreen = { group = "androidx.core", name = "core-splashscreen", version.ref = "splashscreen" }

androidx-navigation-compose = { module = "androidx.navigation:navigation-compose", version.ref = "navigationCompose" }
androidx-navigation-ui-ktx = { module = "androidx.navigation:navigation-ui-ktx", version.ref = "navigationCompose" }
androidx-navigation-fragment-ktx = { module = "androidx.navigation:navigation-fragment-ktx", version.ref = "navigationCompose" }

androidx-media3 = { group = "androidx.media3", name = "media3-exoplayer", version.ref = "exoplayer"}
androidx-media3-ui = { group = "androidx.media3", name = "media3-ui", version.ref = "exoplayer"}
play-services-auth = { group = "com.google.android.gms", name = "play-services-auth", version.ref = "playServicesAuth" }

android-image-cropper = { module = "com.vanniktech:android-image-cropper", version.ref = "androidImageCropper"}
androidx-paging-common-android = { group = "androidx.paging", name = "paging-common-android", version.ref = "pagingCommonAndroid" }
translate = { module = "com.google.mlkit:translate", version.ref = "translate" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
jetbrains-kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
google-dagger-hilt-android = { id = "com.google.dagger.hilt.android", version.ref = "hilt"}
google-gms-google-services = { id = "com.google.gms.google-services", version.ref = "googleGmsGoogleServices" }
google-firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "googleFirebaseCrashlytics" }
google-firebase-firebase-perf = { id = "com.google.firebase.firebase-perf", version.ref = "googleFirebaseFirebasePerf" }
com-google-devtools-ksp = { id = "com.google.devtools.ksp", version.ref = "ksp"}
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }